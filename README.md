# Mews

## Running the application locally


### Create .env file

Sample:
```
# Postgres connection info
POSTGRES_HOST=localhost
POSTGRES_PORT=6432
POSTGRES_USER=admin
POSTGRES_PASSWORD=password
POSTGRES_DB=mews
DATABASE_URL=postgresql://admin:password@localhost:6432/mews

# S3 connection info
S3_REGION=ams3
S3_ENDPOINT_URL=http://localhost:9000
S3_ACCESS_KEY_ID=admin
S3_SECRET_ACCESS_KEY=password
S3_BUCKET_NAME=mri
S3_PORT=9000
CREATE_S3_BUCKET=true

# OpenAI API key
OPENAI_API_KEY=sk-...
```


### Run the dependencies

For Linux Fedora users:
```bash
export COMPOSE=podman-compose
```

Run Postgres and S3 with docker-compose:
```bash
docker-compose up
alias docker-compose='podman-compose'
alias docker='podman'
```

To apply the DB migrations, run:
```bash
make migrate-dev
```


### Run the application
```bash
source .venv/bin/activate
make start-dev
```

Build and run Docker:
```bash
docker build -t my-fastapi-app .
docker run -p 8000:8000 my-fastapi-app
```

Or podman
```bash
podman build -t my-fastapi-app .
podman run -it -p 8000:8000 my-fastapi-app
```


## Process

Before commit run
```bash
make pre-commit
```

Don't push to `main` directly. Create a pull request instead.
The GitHub Actions will run the same checks and report any issues.


## Links

Web application on localhost: http://localhost:8000/
Minio WebUI: http://localhost:9001/

Python libraries:
- https://fastapi.tiangolo.com/ (web framework)
- https://sqlmodel.tiangolo.com/ (ORM)
- https://feedparser.readthedocs.io/ (RSS parser)
- https://procrastinate.readthedocs.io/ (task queue)
- https://trafilatura.readthedocs.io/ (article extractor)

DB migrations tool: https://flywaydb.org/
