version: '3.9'

services:
  db:
    image: postgres:17
    container_name: postgres_17
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - mews_postgres_data:/var/lib/postgresql/data
    networks:
      - mews-net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: [ "postgres", "-c", "log_statement=all" ]

  s3:
    image: minio/minio
    container_name: minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${S3_ACCESS_KEY_ID}
      MINIO_ROOT_PASSWORD: ${S3_SECRET_ACCESS_KEY}
    ports:
      - "${S3_PORT}:9000"
      - 9001:9001 # WebUI
    volumes:
      - mews_s3_data:/data
    networks:
      - mews-net
    command: server /data --console-address ":9001"

  flyway:
    image: flyway/flyway
    profiles:
      - migrate
    entrypoint: >
      flyway
      -url=jdbc:postgresql://db:5432/${POSTGRES_DB}
      -user=${POSTGRES_USER}
      -password=${POSTGRES_PASSWORD}
    command: ""  # allow CLI to append commands like 'migrate'
    volumes:
      - ./migrations:/flyway/sql:Z
    networks:
      - mews-net


networks:
  mews-net:
    name: mews-net


volumes:
  mews_postgres_data:
    name: mews_postgres_data
  mews_s3_data:
    name: mews_s3_data
