alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
databases:
- cluster_name: db-postgresql-fra1-34649
  db_name: defaultdb
  db_user: doadmin
  engine: PG
  name: db-postgresql-fra1-34649
  production: true
  version: "17"
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: mews
    match:
      path:
        prefix: /
name: king-prawn-app
region: fra
services:
- dockerfile_path: /Dockerfile
  envs:
  - key: DATABASE_URL
    scope: RUN_TIME
    value: ${db-postgresql-fra1-34649.DATABASE_URL}
  github:
    branch: main
    deploy_on_push: true
    repo: servletcloud/mews
  http_port: 8000
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb-fixed
  name: mews
  source_dir: /
