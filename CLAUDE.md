# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Environment Setup
- `source .venv/bin/activate` - Activate virtual environment
- `make install` - Install production dependencies
- `make dev-install` - Install development dependencies

### Development Server
- `make start-dev` - Start FastAPI development server (runs on port 8000)
- `make console` - Start IPython console with app context loaded

### Code Quality
- `make format` - Format code with black
- `make lint` - Lint code with ruff (auto-fixes issues)
- `make typecheck` - Run mypy type checking
- `make test` - Run pytest test suite with verbose output
- `make pre-commit` - Run all quality checks (format, lint, typecheck, test)
- `make ci` - Run CI checks (strict versions of quality tools)

### Docker
- `docker build -t my-fastapi-app .` - Build Docker image
- `docker run -p 8000:8000 my-fastapi-app` - Run containerized app
- `make lint-docker` - Lint Dockerfile with hadolint

## Architecture Overview

This is a **news aggregation and processing system** built with FastAPI, using asynchronous task processing for RSS feed monitoring and web page scraping.

### Core Components

**RSS Feed Processing Pipeline:**
- `app/pipeline.py` - Main orchestration layer for RSS feed checking
- `app/rss_scrabber.py` - Downloads and parses RSS feeds, detects new items
- `app/page_scrabber.py` - Downloads full web pages for RSS items
- Uses Procrastinate for background job processing with PostgreSQL

**Data Models (`app/db/models.py`):**
- `Feed` - RSS feed configurations with error tracking
- `FeedItem` - Individual RSS items with metadata
- `WebPage` - Downloaded web page content
- `PageContent` - Binary content storage with checksums
- Uses SQLModel (Pydantic + SQLAlchemy) with async PostgreSQL

**External Services:**
- `app/clients/s3.py` - S3-compatible storage for large content
- `app/clients/gpt.py` - OpenAI integration for content processing
- `app/util/article/` - Article extraction and AI summarization tools

**Configuration (`app/boot.py`):**
- Environment-based settings with Pydantic
- Database connection pooling
- S3 client setup
- OpenAI client initialization
- Procrastinate job queue configuration

### Task Processing Flow
1. Periodic task checks all RSS feeds every 5 minutes
2. Each feed spawns individual check jobs with locking
3. New RSS items trigger web page download jobs
4. Content is stored in S3 and metadata in PostgreSQL
5. Article extraction and summarization can be applied to downloaded pages

### Key Features
- **Concurrent Processing**: Uses asyncio throughout with proper connection pooling
- **Error Resilience**: Tracks consecutive errors per feed, exponential backoff for retries
- **Deduplication**: SHA256 hashing prevents reprocessing same RSS content
- **Content Caching**: TTL-based caching for downloaded web pages
- **Job Locking**: Prevents duplicate processing of same feeds/items

### Database Schema
- `rss` schema: RSS feeds and items
- `public` schema: Web pages and content storage
- `procrastinate` schema: Job queue tables (auto-managed)

## AI Provider Configuration

The system supports multiple AI providers for content processing:

### OpenAI (default)
```bash
AI_PROVIDER=openai
OPENAI_API_KEY=your-openai-key
```

### DeepSeek v3
```bash
AI_PROVIDER=deepseek
DEEPSEEK_API_KEY=your-deepseek-key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1  # optional, defaults to official API
```

### Google Gemini 2.0 Flash
```bash
AI_PROVIDER=gemini
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-2.0-flash-exp  # optional, defaults to gemini-2.0-flash-exp
```

The AI client is automatically configured based on `AI_PROVIDER` environment variable and implements the same interface for all providers.

## Testing

- Tests use pytest with asyncio support
- TestContainers integration for PostgreSQL testing
- Test files mirror the app structure in `tests/` directory
- Use `TESTCONTAINERS_RYUK_DISABLED=true` environment variable for test runs