import asyncio
import httpx
import logging
from app.boot import db_engine, s3_helper, new_snowflake_gen
from app.clients import s3
from app.db import feed_item_repo, models
from app.util import rss
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime


HTTPX_TIMEOUT_SECONDS = 10.0


logger = logging.getLogger(__name__)
http_client = httpx.AsyncClient(timeout=HTTPX_TIMEOUT_SECONDS, follow_redirects=True)


async def check_rss_feed(
    feed_id: int, url: str, last_sha256: str
) -> list[models.FeedItem]:
    error_msg: str | None = None
    new_items: list[models.FeedItem] = []

    try:
        response = await http_client.get(url)
        response.raise_for_status()

        rss_response = rss.parse(response.text)
        sha256 = rss_response.feed.items_sha256()

        if sha256 != last_sha256:
            new_items = await scrab_feed(
                feed_id=feed_id, content=response.content, rss_response=rss_response
            )
    except Exception as e:
        logger.warning(
            "Error downloading or parsing feed %s: %s", url, e, exc_info=True
        )
        error_msg = str(e)

    async with AsyncSession(bind=db_engine) as session:
        feed = await session.get_one(models.Feed, feed_id)

        if error_msg is None:
            feed.mark_download_success(sha256, now=datetime.now())
        else:
            feed.mark_download_error(error_msg, now=datetime.now())

        session.add(feed)
        await session.commit()

    return new_items


async def scrab_feed(
    feed_id: int, content: bytes, rss_response: rss.RssResponse
) -> list[models.FeedItem]:
    new_items = await find_not_existing_items(feed_id, rss_response.feed.items)

    if not new_items:
        return []

    id_gen = new_snowflake_gen()
    snapshot_id = next(id_gen)

    upload_snapshots_task = upload_snapshots(
        feed_id=feed_id,
        snapshot_id=snapshot_id,
        raw=content,
        rss_response=rss_response,
    )

    db_snapshot = models.FeedSnapshot(
        id=snapshot_id,
        feed_id=feed_id,
        new_items_cnt=len(new_items),
    )

    async def prepare_item(rss_item: rss.FeedItem) -> models.FeedItem:
        db_item = create_db_item(next(id_gen), db_snapshot, rss_item)
        await upload_item_to_s3(db_item, rss_item)
        return db_item

    db_new_items = await asyncio.gather(
        *[prepare_item(item) for item in reversed(new_items)]
    )
    db_new_items_copy = [item.model_copy(deep=True) for item in db_new_items]
    await upload_snapshots_task

    async with AsyncSession(bind=db_engine) as session:
        session.add(db_snapshot)
        await session.flush()

        session.add_all(db_new_items)

        await session.commit()

    return db_new_items_copy


async def find_not_existing_items(
    feed_id: int, items: list[rss.FeedItem]
) -> list[rss.FeedItem]:
    async with AsyncSession(bind=db_engine) as session:
        return [
            item
            for item in items
            if item.guid
            and len(item.guid) < models.FeedItem.MAX_TEXT_LEN
            and not await feed_item_repo.exists(
                session=session,
                feed_id=feed_id,
                guid=item.guid,
            )
        ]


async def upload_snapshots(
    feed_id: int, snapshot_id: int, raw: bytes, rss_response: rss.RssResponse
):
    raw_key = s3.feed_snapshot_raw_key(feed_id, snapshot_id)
    parsed_key = s3.feed_snapshot_parsed_key(feed_id, snapshot_id)

    def upload():
        json = rss.to_json_bytes(rss_response)
        s3_helper.upload_sync(key=raw_key, content=raw)
        s3_helper.upload_sync(key=parsed_key, content=json)

    await asyncio.to_thread(upload)


async def upload_item_to_s3(db_item: models.FeedItem, rss_item: rss.FeedItem):
    def upload():
        key = s3.feed_item_raw_key(db_item.feed_id, db_item.id)
        content = rss.to_json(rss_item).encode("utf-8")
        s3_helper.upload_sync(key=key, content=content)

    await asyncio.to_thread(upload)


def create_db_item(
    id: int, snapshot: models.FeedSnapshot, item: rss.FeedItem
) -> models.FeedItem:
    return models.FeedItem(
        id=id,
        feed_id=snapshot.feed_id,
        snapshot_id=snapshot.id,
        guid_crc=feed_item_repo.crc64_str(item.guid) if item.guid else 0,
        guid=item.guid,
        link=(item.link[: models.FeedItem.MAX_TEXT_LEN] if item.link else None),
        title=(item.title[: models.FeedItem.MAX_TEXT_LEN] if item.title else None),
        published_at=item.published_parsed if item.published_parsed else None,
        publisher=(
            item.publisher[: models.FeedItem.MAX_TEXT_LEN] if item.publisher else None
        ),
    )
