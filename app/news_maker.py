from typing import Iterator
from app.db import models
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession


def news_from_rss(id: int, feed_item: models.FeedItem) -> models.News:
    return models.News(
        id=id,
        source_id=feed_item.id,
        source_type=models.SourceTypeEnum.RSS,
        published_at=feed_item.published_at or feed_item.created_at,
        title=feed_item.title or str(feed_item.id),
        original_url=feed_item.link,
    )


def news_from_rss_list(
    id_gen: Iterator[int], feed_items: list[models.FeedItem]
) -> list[models.News]:
    return [
        news_from_rss(next(id_gen), feed_item)
        for feed_item in feed_items
        if feed_item.link
    ]


async def persist_news(db_engine: AsyncEngine, db_news: list[models.News]):
    if not db_news:
        return

    async with AsyncSession(bind=db_engine) as session:
        session.add_all(db_news)
        await session.commit()
