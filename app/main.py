from app import pipeline
from app.boot import procrastinator, check_db_connection, run_procrastinate_worker
from procrastinate import builtin_tasks
from fastapi import FastAPI


@procrastinator.periodic(periodic_id="every_5_minutes", cron="*/5 * * * *")
@procrastinator.task(
    name="rss_scrabber.check_rss_feeds",
    queueing_lock="check_rss_feeds_queue",
    lock="check_rss_feeds_job",
    queue="check_rss_feeds",
)
async def check_rss_feeds(timestamp: int):
    await pipeline.check_rss_feeds()


@procrastinator.periodic(periodic_id="every_hour", cron="0 0 * * * *")
@procrastinator.task(
    name="remove_old_jobs",
    queueing_lock="remove_old_jobs_queue",
    lock="remove_old_jobs_job",
    pass_context=True,
)
async def remove_old_jobs(context, timestamp):
    return await builtin_tasks.remove_old_jobs(
        context,
        max_hours=72,
        remove_failed=True,
        remove_cancelled=True,
        remove_aborted=True,
    )


app = FastAPI(
    on_startup=[
        check_db_connection,
        run_procrastinate_worker,
    ],
    on_shutdown=[procrastinator.close_async],
)


@app.get("/")
async def root():
    return {"message": "Hui Pizda i Djigurda! Renault clitor!"}
