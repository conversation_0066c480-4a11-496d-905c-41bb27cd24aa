import logging
import httpx
from typing import Dict, Any, Optional
from dataclasses import dataclass
from app.util.article.types import UsageInfo

logger = logging.getLogger(__name__)


@dataclass
class Usage:
    """Token usage information for Gemini API calls"""

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class GeminiClient:
    """Gemini API client using direct HTTP requests"""

    def __init__(
        self, api_key: str, model: str = "gemini-2.0-flash-exp", timeout: float = 60.0
    ):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.http_client = httpx.AsyncClient(timeout=timeout)

    async def close(self):
        """Close the HTTP client"""
        await self.http_client.aclose()

    async def generate_content(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: float = 0.0,
        max_tokens: int = 4096,
    ) -> Dict[str, Any]:
        """Generate content using Gemini API"""

        model_name = model or self.model
        url = f"{self.base_url}/models/{model_name}:generateContent"

        headers = {
            "Content-Type": "application/json",
        }

        # Add API key as query parameter (Gemini API style)
        params = {"key": self.api_key}

        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": temperature,
                "maxOutputTokens": max_tokens,
            },
        }

        response = await self.http_client.post(
            url,
            headers=headers,
            params=params,
            json=payload,
        )

        response.raise_for_status()
        return response.json()


class Client:
    """Gemini client that implements the same interface as OpenAI and DeepSeek clients"""

    PROVIDER_NAME = "gemini"

    # Gemini 2.0 Flash pricing: ~$0.075/1M input tokens, ~$0.30/1M output tokens
    INPUT_COST_PER_TOKEN = 0.075 / 1_000_000
    OUTPUT_COST_PER_TOKEN = 0.30 / 1_000_000

    def __init__(self, client: GeminiClient):
        self.client = client
        self._last_usage: Optional[UsageInfo] = None

    async def query_as_user(self, prompt: str) -> str:
        """Query Gemini with a user prompt, compatible with OpenAI client interface"""
        try:
            logger.info(f"Gemini query using model: {self.get_model_name()}")
            result = await self.client.generate_content(
                prompt=prompt,
                temperature=0.0,
                max_tokens=4096,
            )

            # Extract content from Gemini response
            content = self.__extract_content(result)
            self.__log_usage(result)

            return content

        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            raise ValueError(f"Gemini API error: {e}")

    def get_model_name(self) -> str:
        return self.client.model

    def get_provider_name(self) -> str:
        return self.PROVIDER_NAME

    def get_last_usage(self) -> Optional[UsageInfo]:
        return self._last_usage

    def __extract_content(self, response: Dict[str, Any]) -> str:
        """Extract text content from Gemini API response"""
        if not response:
            raise ValueError("Gemini returned empty response")

        # Standard Gemini API response structure
        if "candidates" in response and len(response["candidates"]) > 0:
            candidate = response["candidates"][0]
            if "content" in candidate and "parts" in candidate["content"]:
                parts = candidate["content"]["parts"]
                if len(parts) > 0 and "text" in parts[0]:
                    return parts[0]["text"]

        # Fallback for other response formats
        if isinstance(response, str):
            return response

        if isinstance(response, dict):
            if "text" in response:
                return response["text"]
            elif "content" in response:
                return response["content"]

        raise ValueError("Gemini returned no usable content")

    def __log_usage(self, response: Dict[str, Any]) -> None:
        """Log token usage for Gemini API calls"""
        try:
            # Extract usage info from Gemini response
            usage_info = response.get("usageMetadata")

            if usage_info:
                prompt_tokens = usage_info.get("promptTokenCount", 0)
                completion_tokens = usage_info.get("candidatesTokenCount", 0)
                total_tokens = prompt_tokens + completion_tokens

                prompt_cost = prompt_tokens * self.INPUT_COST_PER_TOKEN
                completion_cost = completion_tokens * self.OUTPUT_COST_PER_TOKEN
                total_cost = prompt_cost + completion_cost

                self._last_usage = UsageInfo(
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens,
                    cost_usd=total_cost,
                    model=self.get_model_name(),
                    provider=self.PROVIDER_NAME,
                )

                logger.info(
                    "Gemini usage - model:%s prompt:%d completion:%d total_cost:$%.4f",
                    self.get_model_name(),
                    prompt_tokens,
                    completion_tokens,
                    total_cost,
                )
            else:
                logger.info(
                    f"Gemini API call completed using model: {self.get_model_name()} (usage info not available)"
                )

        except Exception as e:
            logger.warning(f"Could not log Gemini usage: {e}")
