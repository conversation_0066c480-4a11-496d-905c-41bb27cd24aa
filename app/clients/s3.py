import io
import logging
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


def page_content_key(content_hash: bytes) -> str:
    return f"mews/page_content/{content_hash.hex()}.html"


def feed_snapshot_raw_key(feed_id: int, snapshot_id: int) -> str:
    return f"mews/feed/{feed_id}/snapshot/{snapshot_id}/raw.xml"


def feed_snapshot_parsed_key(feed_id: int, snapshot_id: int) -> str:
    return f"mews/feed/{feed_id}/snapshot/{snapshot_id}/parsed.json"


def feed_item_raw_key(feed_id: int, item_id: int) -> str:
    return f"mews/feed/{feed_id}/item/{item_id}/raw.json"


def article_short_key(article_id: int) -> str:
    return f"mews/article/{article_id}/short.md.gz"


def article_original_key(article_id: int) -> str:
    return f"mews/article/{article_id}/original.md.gz"


class S3:
    def __init__(self, s3_client, bucket_name: str):
        self.s3_client = s3_client
        self.bucket_name = bucket_name

    def ensure_bucket_exists(self):
        try:
            create_kwargs = {"Bucket": self.bucket_name}
            self.s3_client.create_bucket(**create_kwargs)
        except ClientError as create_exc:
            if create_exc.response["Error"]["Code"] == "BucketAlreadyOwnedByYou":
                return
            raise

    def upload_sync(
        self, key: str, content: bytes, extra_args: dict[str, str] | None = None
    ):
        logger.info("Uploading file %s", key)

        self.s3_client.upload_fileobj(
            Bucket=self.bucket_name,
            Key=key,
            Fileobj=io.BytesIO(content),
            ExtraArgs=extra_args,
        )

        logger.info("Uploaded file %s", key)

    def get_sync(self, key: str) -> bytes:
        obj = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
        return obj["Body"].read()
