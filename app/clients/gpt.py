from openai import Async<PERSON><PERSON>AI
from openai.types.chat import Chat<PERSON><PERSON>pletion
import logging
from typing import Optional
from app.helpers.openai_helper import cost_usd
from app.util.article.types import UsageInfo

logger = logging.getLogger(__name__)


class Client:
    MODEL_NAME = "gpt-4o"
    PROVIDER_NAME = "openai"

    def __init__(self, client: AsyncOpenAI):
        self.client = client
        self._last_usage: Optional[UsageInfo] = None

    async def query_as_user(self, prompt: str) -> str:
        logger.info(f"OpenAI query using model: {self.MODEL_NAME}")
        response = await self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
        )

        self.__log_usage(response)

        return self.__must_get_content(response)

    def get_model_name(self) -> str:
        return self.MODEL_NAME

    def get_provider_name(self) -> str:
        return self.PROVIDER_NAME

    def get_last_usage(self) -> Optional[UsageInfo]:
        return self._last_usage

    def __log_usage(self, response: ChatCompletion):
        usage = response.usage
        if usage:
            cost = cost_usd(usage)
            self._last_usage = UsageInfo(
                prompt_tokens=usage.prompt_tokens,
                completion_tokens=usage.completion_tokens,
                total_tokens=usage.total_tokens,
                cost_usd=cost,
                model=self.MODEL_NAME,
                provider=self.PROVIDER_NAME,
            )
            logger.info(
                "OpenAI usage - model:%s prompt:%d completion:%d total_cost:$%.4f",
                self.MODEL_NAME,
                usage.prompt_tokens,
                usage.completion_tokens,
                cost,
            )

    def __must_get_content(self, response: ChatCompletion) -> str:
        choices = response.choices

        if not choices:
            raise ValueError("OpenAI returned no choices")

        content = choices[0].message.content
        if not content:
            raise ValueError("OpenAI returned no content")

        return content
