import httpx
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from app.util.article.types import UsageInfo

logger = logging.getLogger(__name__)


@dataclass
class Usage:
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


@dataclass
class Message:
    role: str
    content: str


@dataclass
class Choice:
    message: Message


@dataclass
class ChatCompletion:
    choices: List[Choice]
    usage: Usage


class DeepSeekClient:
    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.deepseek.com/v1",
        timeout: float = 60.0,
    ):
        self.api_key = api_key
        self.base_url = base_url
        self.http_client = httpx.AsyncClient(timeout=timeout)

    async def close(self):
        await self.http_client.aclose()

    async def chat_completions_create(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stream: bool = False,
    ) -> ChatCompletion:
        """Create a chat completion using DeepSeek API"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p,
            "frequency_penalty": frequency_penalty,
            "presence_penalty": presence_penalty,
            "stream": stream,
        }

        response = await self.http_client.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
        )

        response.raise_for_status()
        data = response.json()

        # Convert response to our dataclass format
        usage = Usage(
            prompt_tokens=data["usage"]["prompt_tokens"],
            completion_tokens=data["usage"]["completion_tokens"],
            total_tokens=data["usage"]["total_tokens"],
        )

        choices = [
            Choice(
                message=Message(
                    role=choice["message"]["role"], content=choice["message"]["content"]
                )
            )
            for choice in data["choices"]
        ]

        return ChatCompletion(choices=choices, usage=usage)


class Client:
    """DeepSeek client that implements the same interface as the OpenAI client"""

    MODEL_NAME = "deepseek-chat"
    PROVIDER_NAME = "deepseek"

    # DeepSeek v3 pricing: ~$0.27/1M input tokens, ~$1.10/1M output tokens
    INPUT_COST_PER_TOKEN = 0.27 / 1_000_000
    OUTPUT_COST_PER_TOKEN = 1.10 / 1_000_000

    def __init__(self, client: DeepSeekClient):
        self.client = client
        self._last_usage: Optional[UsageInfo] = None

    async def query_as_user(self, prompt: str) -> str:
        """Query DeepSeek with a user prompt, compatible with OpenAI client interface"""
        logger.info(f"DeepSeek query using model: {self.MODEL_NAME}")
        response = await self.client.chat_completions_create(
            model=self.MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
        )

        self.__log_usage(response)
        return self.__must_get_content(response)

    def get_model_name(self) -> str:
        return self.MODEL_NAME

    def get_provider_name(self) -> str:
        return self.PROVIDER_NAME

    def get_last_usage(self) -> Optional[UsageInfo]:
        return self._last_usage

    def __log_usage(self, response: ChatCompletion):
        """Log token usage - DeepSeek pricing is different from OpenAI"""
        usage = response.usage
        if usage:
            prompt_cost = usage.prompt_tokens * self.INPUT_COST_PER_TOKEN
            completion_cost = usage.completion_tokens * self.OUTPUT_COST_PER_TOKEN
            total_cost = prompt_cost + completion_cost

            self._last_usage = UsageInfo(
                prompt_tokens=usage.prompt_tokens,
                completion_tokens=usage.completion_tokens,
                total_tokens=usage.total_tokens,
                cost_usd=total_cost,
                model=self.MODEL_NAME,
                provider=self.PROVIDER_NAME,
            )

            logger.info(
                "DeepSeek usage - model:%s prompt:%d completion:%d total_cost:$%.4f",
                self.MODEL_NAME,
                usage.prompt_tokens,
                usage.completion_tokens,
                total_cost,
            )

    def __must_get_content(self, response: ChatCompletion) -> str:
        """Extract content from response, ensuring it exists"""
        choices = response.choices

        if not choices:
            raise ValueError("DeepSeek returned no choices")

        content = choices[0].message.content
        if not content:
            raise ValueError("DeepSeek returned no content")

        return content
