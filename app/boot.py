import asyncio
import logging
from typing import Union

import boto3
import procrastinate
from botocore.config import Config
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from snowflake import SnowflakeGenerator
from app.init import Settings
from app.init.settings import <PERSON><PERSON><PERSON><PERSON>, assert_never
from app.clients import s3
from app.clients import gpt
from app.clients import deepseek
from app.clients import gemini

# Constants
DEFAULT_HTTP_TIMEOUT = 60.0
DEFAULT_DB_POOL_SIZE = 2
DEFAULT_DB_MAX_OVERFLOW = 8
DEFAULT_DB_POOL_TIMEOUT = 30
DEFAULT_DB_POOL_RECYCLE = 1800


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
)


settings = Settings()  # type: ignore[call-arg]


procrastinator = procrastinate.App(
    connector=procrastinate.PsycopgConnector(
        conninfo=settings.database_url_sync,
        kwargs={"options": "-c search_path=procrastinate"},
    )
)


async def run_procrastinate_worker():
    await procrastinator.open_async()
    # procrastinator.schema_manager.apply_schema()
    asyncio.create_task(procrastinator.run_worker_async(install_signal_handlers=True))


db_engine = create_async_engine(
    settings.database_url_async,
    echo=True,
    pool_size=DEFAULT_DB_POOL_SIZE,  # max number of connections in the pool
    max_overflow=DEFAULT_DB_MAX_OVERFLOW,  # additional connections allowed beyond pool_size
    pool_timeout=DEFAULT_DB_POOL_TIMEOUT,  # seconds to wait before giving up on getting a connection
    pool_recycle=DEFAULT_DB_POOL_RECYCLE,  # recycle connections after 30 mins (prevent stale)
)


async def check_db_connection() -> None:
    async with db_engine.connect() as conn:
        result = await conn.execute(text("SELECT 1"))
        result.scalar_one()


s3_boto_client = boto3.session.Session().client(
    "s3",
    region_name=settings.s3_region,
    endpoint_url=settings.s3_endpoint_url,
    aws_access_key_id=settings.s3_access_key_id,
    aws_secret_access_key=settings.s3_secret_access_key,
    config=Config(signature_version="s3v4"),
)

s3_helper = s3.S3(s3_boto_client, settings.s3_bucket_name)

if settings.create_s3_bucket:
    s3_helper.ensure_bucket_exists()


def new_snowflake_gen():
    return SnowflakeGenerator(settings.snowflake_id)


def __new_openai_client(
    api_key: str, http_timeout: float = DEFAULT_HTTP_TIMEOUT
) -> gpt.Client:
    from httpx import AsyncClient
    from openai import AsyncOpenAI

    http_client = AsyncClient(timeout=http_timeout)
    openai_client = AsyncOpenAI(api_key=api_key, http_client=http_client)
    return gpt.Client(openai_client)


def __new_deepseek_client(
    api_key: str, base_url: str, http_timeout: float = DEFAULT_HTTP_TIMEOUT
) -> deepseek.Client:
    deepseek_client = deepseek.DeepSeekClient(
        api_key=api_key, base_url=base_url, timeout=http_timeout
    )
    return deepseek.Client(deepseek_client)


def __new_gemini_client(
    api_key: str, model: str, http_timeout: float = DEFAULT_HTTP_TIMEOUT
) -> gemini.Client:
    gemini_client = gemini.GeminiClient(
        api_key=api_key, model=model, timeout=http_timeout
    )
    return gemini.Client(gemini_client)


# Initialize AI client based on configuration
ai_client: Union[deepseek.Client, gemini.Client, gpt.Client]
match settings.ai_provider:
    case AiProvider.DEEPSEEK:
        ai_client = __new_deepseek_client(
            api_key=settings.deepseek_api_key,  # type: ignore
            base_url=settings.deepseek_base_url,
            http_timeout=DEFAULT_HTTP_TIMEOUT,
        )
    case AiProvider.GEMINI:
        ai_client = __new_gemini_client(
            api_key=settings.gemini_api_key, model=settings.gemini_model, http_timeout=DEFAULT_HTTP_TIMEOUT  # type: ignore
        )
    case AiProvider.OPENAI:
        ai_client = __new_openai_client(api_key=settings.openai_api_key, http_timeout=DEFAULT_HTTP_TIMEOUT)  # type: ignore
    case _:
        assert_never(settings.ai_provider)

# Keep backwards compatibility
openai_client = ai_client
