from bs4 import BeautifulSoup, Comment, NavigableString, Tag
from urllib.parse import urljoin


def new_soup(html: str) -> BeautifulSoup:
    return BeautifulSoup(html, "html.parser")


def decompose_tags(soup: BeautifulSoup, tags: list[str]):
    for tag in soup(tags):
        tag.decompose()


def extract_comments(soup: BeautifulSoup):
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()


def remove_intertag_whitespace(soup: BeautifulSoup):
    for element in soup.find_all(string=True):
        if isinstance(element, NavigableString) and element.strip() == "":
            element.extract()


def remove_empty_tags(soup: BeautifulSoup, except_tags: list[str] = []):
    while True:
        removed = False
        for element in soup.find_all():
            if isinstance(element, Tag):
                if element.name not in except_tags and not element.get_text(strip=True):
                    element.decompose()
                    removed = True
        if not removed:
            break


def absolutize_links(soup: BeautifulSoup, base_url: str):
    link_attrs = {
        "a": "href",
        "link": "href",
        "img": "src",
        "script": "src",
        "iframe": "src",
        "source": "src",
        "video": "src",
        "audio": "src",
    }

    absolute_prefixes = ("http://", "https://", "//", "data:")

    for tag_name, attr in link_attrs.items():
        for tag in soup.find_all(tag_name, attrs={attr: True}):
            if not isinstance(tag, Tag):
                continue

            raw_url = tag[attr]
            if not isinstance(raw_url, str):
                continue

            url = raw_url

            is_absolute = url.startswith(absolute_prefixes)
            if is_absolute:
                continue

            has_scheme = (
                ":" in url.split("/", 1)[0]
            )  # catches mailto:, tel:, javascript:, etc.
            if has_scheme:
                continue

            tag[attr] = urljoin(base_url, url)
