from lingua import LanguageDetectorBuilder


class LanguageDetector:
    def __init__(self):
        self.detector = LanguageDetectorBuilder.from_all_languages().build()

    def detect_iso_639_1(self, text: str) -> str | None:
        if not text or not text.strip():
            return None

        language = self.detector.detect_language_of(text)

        if not language:
            return None

        return language.iso_code_639_1.name.lower()


def word_count(text: str) -> int:
    """Return the number of whitespace-separated words in ``text``."""
    return len(text.split())
