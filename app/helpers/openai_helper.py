import re

PROMPT_COST_USD = 0.005 / 1000
COMPLETION_COST_USD = 0.02 / 1000


def cost_usd(usage) -> float:
    prompt_usd = usage.prompt_tokens * PROMPT_COST_USD
    completion_usd = usage.completion_tokens * COMPLETION_COST_USD
    return prompt_usd + completion_usd


def strip_markdown(content: str) -> str:
    content = content.strip()

    match = re.fullmatch(r"```markdown\s*(.*?)\s*```", content, re.DOTALL)
    if match:
        content = match.group(1).strip()

    return content
