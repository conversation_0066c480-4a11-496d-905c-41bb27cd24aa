import markdown2
from bs4 import BeautifulSoup
import re


def extract_text(md_text: str) -> str:
    html = markdown2.markdown(md_text, extras=["sane_lists"])
    soup = BeautifulSoup(html, "html.parser")
    text = soup.get_text(separator=" ", strip=True)
    text = " ".join(text.split())  # remove extra whitespace
    return text


def extract_title(md_text: str, limit: int = 200) -> str:
    """Extract the first header or first sentence from markdown text."""

    html = markdown2.markdown(md_text, extras=["sane_lists"])
    soup = BeautifulSoup(html, "html.parser")

    header_text = None
    for level in range(1, 7):
        header = soup.find(f"h{level}")
        if header and header.get_text(strip=True):
            header_text = header.get_text(strip=True)
            break

    if not header_text:
        plain_text = extract_text(md_text)
        # Capture text up to the first sentence-ending punctuation
        # (period, exclamation mark, or question mark)
        # followed by whitespace or end of string.
        match = re.search(r"(.+?[.!?])(?:\s|$)", plain_text)
        header_text = match.group(1) if match else plain_text

    header_text = header_text.strip()

    if len(header_text) > limit:
        header_text = header_text[:limit].rstrip() + "..."

    return header_text
