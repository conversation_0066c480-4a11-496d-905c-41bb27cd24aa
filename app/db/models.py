import hashlib
from enum import Enum
from sqlmodel import SQLModel, <PERSON>
from typing import ClassVar
from datetime import datetime
from sqlalchemy import (
    BigInteger,
    DateTime,
    Column,
    SmallInteger,
    Enum as SAEnum,
    UniqueConstraint,
)

ERROR_MSG_MAX_LEN = 2048


class TimestampZ(DateTime):
    def __init__(self, **kw):
        super().__init__(timezone=True, **kw)


def enum_values(enum_cls):
    return [e.value for e in enum_cls]


class Feed(SQLModel, table=True):
    __table_args__ = {"schema": "rss"}
    __tablename__ = "feeds"

    id: int = Field(sa_type=BigInteger, default=None, primary_key=True)
    url: str = Field(unique=True)
    enabled: bool = Field(default=True)
    cr_articles: bool = Field(default=False)
    consec_err_cnt: int = Field(default=0)
    last_downloaded_at: datetime = Field(
        sa_type=TimestampZ, default=datetime(2025, 1, 1, 0, 0, 0)
    )
    last_downloaded_sha256: str = Field(default="")
    last_error_msg: str | None = Field(default=None)

    created_at: datetime = Field(sa_type=TimestampZ, default_factory=datetime.now)

    def mark_download_success(self, sha256: str, now: datetime) -> None:
        self.last_downloaded_sha256 = sha256
        self.last_downloaded_at = now
        self.consec_err_cnt = 0
        self.last_error_msg = None

    def mark_download_error(self, error_msg: str, now: datetime) -> None:
        self.last_downloaded_at = now
        self.consec_err_cnt += 1
        self.last_error_msg = error_msg[:ERROR_MSG_MAX_LEN]


class FeedSnapshot(SQLModel, table=True):
    __tablename__ = "feed_snapshots"
    __table_args__ = {"schema": "rss"}

    id: int = Field(sa_type=BigInteger, primary_key=True)
    feed_id: int = Field(sa_type=BigInteger, foreign_key="rss.feeds.id")
    new_items_cnt: int = Field(default=0)
    created_at: datetime = Field(sa_type=TimestampZ, default_factory=datetime.now)


class FeedItem(SQLModel, table=True):
    __tablename__ = "feed_items"
    __table_args__ = {"schema": "rss"}

    MAX_TEXT_LEN: ClassVar[int] = 1024

    id: int = Field(sa_type=BigInteger, primary_key=True)
    feed_id: int = Field(sa_type=BigInteger, foreign_key="rss.feeds.id")
    snapshot_id: int = Field(sa_type=BigInteger, foreign_key="rss.feed_snapshots.id")
    web_page_id: int | None = Field(sa_type=BigInteger)

    guid_crc: int = Field(sa_type=BigInteger)
    guid: str = Field(max_length=MAX_TEXT_LEN)

    link: str | None = Field(max_length=MAX_TEXT_LEN)
    title: str | None = Field(max_length=MAX_TEXT_LEN)
    publisher: str | None = Field(max_length=MAX_TEXT_LEN)

    created_at: datetime = Field(sa_type=TimestampZ, default_factory=datetime.now)
    published_at: datetime | None = Field(sa_type=TimestampZ)


class Page(SQLModel, table=True):
    __tablename__ = "pages"
    __table_args__ = {"schema": "public"}

    MAX_URL_LENGTH: ClassVar[int] = 1024

    id: int = Field(sa_type=BigInteger, primary_key=True)
    url: str = Field(max_length=MAX_URL_LENGTH)
    created_at: datetime = Field(sa_type=TimestampZ, default_factory=datetime.now)
    size: int = Field(sa_type=BigInteger)
    sha256: bytes = Field()

    def update_with_content(self, content: bytes):
        self.size = len(content)
        self.sha256 = hashlib.sha256(content).digest()


class Article(SQLModel, table=True):
    __tablename__ = "articles"
    __table_args__ = {"schema": "public"}

    id: int = Field(sa_type=BigInteger, primary_key=True)

    original_length_chars: int = Field(sa_type=SmallInteger)
    original_length_words: int = Field(sa_type=SmallInteger)
    shorten_length_chars: int = Field(sa_type=SmallInteger)
    shorten_length_words: int = Field(sa_type=SmallInteger)
    summary_length_chars: int = Field(sa_type=SmallInteger)
    summary_length_words: int = Field(sa_type=SmallInteger)

    has_long_version: bool = Field()
    is_extracted_by_ai: bool = Field()
    cleaned_by_ai: bool = Field()

    created_at: datetime = Field(sa_type=TimestampZ, default_factory=datetime.now)

    title: str = Field(max_length=1024)
    summary: str | None = Field(default=None, max_length=2024)
    language: str | None = Field(default=None, max_length=10)


class SourceTypeEnum(str, Enum):
    RSS = "rss"
    WEB = "web"
    TELEGRAM = "telegram"


class News(SQLModel, table=True):
    __tablename__ = "news"
    __table_args__ = (
        UniqueConstraint("source_type", "source_id", name="news_unique_source_type_id"),
        {"schema": "public"},
    )

    id: int = Field(sa_type=BigInteger, primary_key=True)

    source_type: SourceTypeEnum = Field(
        sa_column=Column(
            SAEnum(
                SourceTypeEnum, name="source_type_enum", values_callable=enum_values
            ),
            nullable=False,
        )
    )

    source_id: int = Field(sa_type=BigInteger)

    article_id: int | None = Field(sa_type=BigInteger, foreign_key="public.articles.id")
    published_at: datetime = Field(sa_type=TimestampZ)
    created_at: datetime = Field(sa_type=TimestampZ, default_factory=datetime.now)

    title: str = Field(max_length=1024)
    original_url: str = Field(max_length=1024)
