from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select, desc
from .models import Page
from datetime import datetime


async def find_by_url(session: AsyncSession, url: str, since: datetime) -> Page | None:
    query = (
        select(Page)
        .where(
            Page.url == url,
            Page.created_at >= since,
        )
        .order_by(desc(Page.id))
        .limit(1)
    )

    rows = await session.execute(query)
    return rows.scalar_one_or_none()
