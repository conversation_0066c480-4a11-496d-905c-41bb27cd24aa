from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from .models import FeedItem
from .util import crc64_str


async def exists(session: AsyncSession, feed_id: int, guid: str) -> bool:
    query = (
        select(FeedItem.id)
        .where(
            FeedItem.feed_id == feed_id,
            FeedItem.guid_crc == crc64_str(guid),
            FeedItem.guid == guid,
        )
        .limit(1)
    )

    rows = await session.execute(query)
    return rows.scalar_one_or_none() is not None
