from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from .models import News, SourceTypeEnum


async def get_by_type(
    session: AsyncSession, source_type: SourceTypeEnum, source_id: int
) -> News:
    query = (
        select(News)
        .where(
            News.source_type == source_type,
            News.source_id == source_id,
        )
        .limit(1)
    )

    rows = await session.execute(query)
    return rows.scalar_one()
