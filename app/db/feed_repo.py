from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from typing import AsyncGenerator
from sqlmodel import select
from .models import Feed


MAX_CONSEC_ERR_CNT = 100
READ_INTERVAL_SECONDS = 60 * 15  # 15 minutes


async def read_all_to_check(
    session: AsyncSession,
    now: datetime,
    max_consec_err_cnt: int = MAX_CONSEC_ERR_CNT,
) -> AsyncGenerator[Feed, None]:
    threshold = now - timedelta(seconds=READ_INTERVAL_SECONDS)

    statement = select(Feed).where(
        Feed.enabled,
        Feed.consec_err_cnt < max_consec_err_cnt,
        Feed.last_downloaded_at < threshold,
    )

    result = await session.stream(statement)

    async for feed in result.scalars():
        yield feed
