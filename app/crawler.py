import asyncio
from snowflake import SnowflakeGenerator
from app.clients import s3
from app.db import page_repo, models
from app.helpers.url_helper import remove_fragment, is_https
from url_normalize import url_normalize
import httpx
import logging
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from datetime import datetime, timedelta, UTC

logger = logging.getLogger(__name__)

HTTPX_TIMEOUT_SECONDS = 10.0
MAX_REDIRECTS = 5
MAX_PAGE_SIZE = 10 * 1024 * 1024  # 10MB

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/113.0.0.0 Safari/537.36"
}
http_client = httpx.AsyncClient(
    timeout=HTTPX_TIMEOUT_SECONDS, follow_redirects=False, headers=headers
)


class TooManyRedirectsError(Exception):
    pass


class TooLongUrlError(Exception):
    pass


class NotHttpsError(Exception):
    pass


class PageTooLargeError(Exception):
    pass


class Crawler:
    def __init__(
        self,
        db_engine: AsyncEngine,
        s3_client: s3.S3,
        id_generator: SnowflakeGenerator,
        http_client: httpx.AsyncClient = http_client,
        max_redirects: int = MAX_REDIRECTS,
        max_page_size: int = MAX_PAGE_SIZE,
    ):
        self.db_engine = db_engine
        self.s3_client = s3_client
        self.http_client = http_client
        self.id_generator = id_generator
        self.max_redirects = max_redirects
        self.max_page_size = max_page_size

    async def get(self, url: str, ttl_seconds: int) -> models.Page:
        now_utc = datetime.now(UTC)
        since = now_utc - timedelta(seconds=ttl_seconds)

        urls = [self.__normalize_url(url)]
        redirects = 0

        while redirects < self.max_redirects:
            last_url = urls[-1]

            cached_page = await self.__find_page(last_url, since)
            if cached_page:
                return cached_page

            response = await self.http_client.get(last_url)

            if response.is_redirect:
                redirects += 1
                if redirects > self.max_redirects:
                    raise TooManyRedirectsError(f"Too many redirects: {urls}")

                normalized = self.__normalize_url(response.headers["location"])
                urls.append(normalized)
                continue

            response.raise_for_status()

            if len(response.content) > self.max_page_size:
                raise PageTooLargeError(f"Page too large: {last_url}")

            return await self.__save_page(urls, response.content, now_utc)

        raise TooManyRedirectsError(f"Too many redirects: {urls}")

    async def __find_page(self, url: str, since: datetime) -> models.Page | None:
        async with AsyncSession(bind=self.db_engine) as session:
            page = await page_repo.find_by_url(session, url, since)
            return page.model_copy(deep=True) if page else None

    async def __save_page(
        self, urls: list[str], content: bytes, now_utc: datetime
    ) -> models.Page:
        pages = [
            models.Page(id=next(self.id_generator), url=url, created_at=now_utc)
            for url in urls
        ]

        for page in pages:
            page.update_with_content(content)

        last_page = pages[-1].model_copy(deep=True)

        s3_key = s3.page_content_key(last_page.sha256)
        await asyncio.to_thread(self.s3_client.upload_sync, s3_key, content)

        async with AsyncSession(bind=self.db_engine) as session:
            session.add_all(pages)
            await session.commit()

        return last_page

    def __normalize_url(self, url: str) -> str:
        # Lowercasing scheme and host
        # Percent-encoding normalization
        # Default port removal (:80/:443)
        # Sorting query parameters
        # Unicode normalization
        normalized = url_normalize(url) or url

        if len(url) > models.Page.MAX_URL_LENGTH:
            raise TooLongUrlError(f"Url too long: {url}")

        if not is_https(normalized):
            raise NotHttpsError(f"Url is not https: {normalized}")

        return remove_fragment(normalized)
