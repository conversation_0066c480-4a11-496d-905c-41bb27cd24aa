import hashlib
import json
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass(frozen=True)
class AuthorDetail:
    name: str | None
    href: str | None
    email: str | None


@dataclass(frozen=True)
class Content:
    value: str | None
    type: str | None
    language: str | None
    base: str | None


@dataclass(frozen=True)
class Enclosure:
    href: str | None
    length: int | None
    type: str | None


@dataclass(frozen=True)
class Link:
    rel: str | None
    type: str | None
    href: str | None
    title: str | None


@dataclass(frozen=True)
class InfoDetails:
    value: str | None
    type: str | None
    language: str | None
    base: str | None


@dataclass(frozen=True)
class Tag:
    term: str | None
    scheme: str | None
    label: str | None


@dataclass(frozen=True)
class Source:
    # Identification
    id: str | None
    title: str | None
    title_detail: InfoDetails | None
    subtitle: str | None
    subtitle_detail: InfoDetails | None

    # Links and media
    link: str | None
    links: list[Link]
    icon: str | None
    logo: str | None

    # Authorship
    author: str | None
    author_detail: AuthorDetail | None
    contributors: list[AuthorDetail] | None

    # Content metadata
    language: str | None
    tags: list[Tag]

    # Timestamps
    published: str | None
    published_parsed: datetime | None
    updated: str | None
    updated_parsed: datetime | None

    # Rights
    rights: str | None
    rights_detail: InfoDetails | None


@dataclass(frozen=True)
class FeedItem:
    # Identification
    id: str | None
    title: str | None
    title_detail: InfoDetails | None

    # Links and media
    link: str | None
    links: list[Link]
    enclosures: list[Enclosure] | None

    # Content
    content: list[Content] | None
    summary: str | None
    summary_detail: InfoDetails | None

    # Authorship
    publisher: str | None
    publisher_detail: AuthorDetail | None
    author: str | None
    author_detail: AuthorDetail | None
    contributors: list[AuthorDetail] | None

    # Timestamps
    published: str | None
    published_parsed: datetime | None
    created: str | None
    created_parsed: datetime | None
    expired: str | None
    expired_parsed: datetime | None
    updated: str | None
    updated_parsed: datetime | None

    # Metadata
    source: Source | None
    tags: list[Tag]
    comments: str | None

    # Rights
    license: str | None

    @property
    def guid(self) -> str | None:
        return self.id or self.link


@dataclass(frozen=True)
class GeneratorDetail:
    name: str | None
    href: str | None
    version: str | None


@dataclass(frozen=True)
class Image:
    title: str | None
    href: str | None
    link: str | None
    width: int | None
    height: int | None
    description: str | None


@dataclass(frozen=True)
class TextInput:
    title: str | None
    link: str | None
    name: str | None
    description: str | None


@dataclass(frozen=True)
class Feed:
    # Identification
    id: str | None
    title: str | None
    title_detail: InfoDetails | None
    subtitle: str | None
    subtitle_detail: InfoDetails | None

    # Links and media
    link: str | None
    links: list[Link]
    icon: str | None
    image: Image | None
    logo: str | None

    # Authorship
    author: str | None
    author_detail: AuthorDetail | None
    contributors: list[AuthorDetail] | None
    publisher: str | None
    publisher_detail: AuthorDetail | None

    # Content metadata
    language: str | None
    tags: list[Tag]

    # Timestamps
    published: str | None
    published_parsed: datetime | None
    updated: str | None
    updated_parsed: datetime | None

    # Feed items
    items: list[FeedItem]

    # Additional information
    docs: str | None
    errorreportsto: str | None
    generator: str | None
    generator_detail: GeneratorDetail | None
    info: str | None
    info_detail: InfoDetails | None

    # Rights and licensing
    license: str | None
    rights: str | None
    rights_detail: InfoDetails | None

    # User input
    text_input: TextInput | None

    def items_sha256(self) -> str:
        guids = [item.guid for item in self.items if item.guid]
        guids.sort()
        return hashlib.sha256("".join(guids).encode()).hexdigest()


@dataclass(frozen=True)
class RssResponse:
    # Request information
    href: str | None
    status: int | None
    headers: dict[str, str]
    modified: str | None

    # Feed metadata
    encoding: str | None
    version: str | None
    namespaces: dict[str, str]

    # Error handling
    bozo_error: bool
    bozo_error_message: str | None

    # Feed content
    feed: Feed


def to_json_bytes(obj) -> bytes:
    return to_json(obj).encode("utf-8")


def to_json(obj) -> str:
    return json.dumps(asdict(obj), indent=2, default=str, ensure_ascii=False)
