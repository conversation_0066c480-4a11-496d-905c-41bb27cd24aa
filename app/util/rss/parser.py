from datetime import datetime
from typing import Any
import feedparser

from .types import (
    RssResponse,
    Feed,
    FeedItem,
    GeneratorDetail,
    TextInput,
    Enclosure,
    Content,
    AuthorDetail,
    Image,
    Link,
    InfoDetails,
    Tag,
    Source,
)


def parse(url_or_body: str) -> RssResponse:
    parsed = feedparser.parse(url_or_body)

    return RssResponse(
        # Request information
        href=parsed.get("href"),
        status=parsed.get("status"),
        headers=parsed.get("headers", {}),
        modified=parsed.get("modified"),
        # Feed metadata
        encoding=parsed.get("encoding"),
        version=parsed.get("version"),
        namespaces=parsed.get("namespaces", {}),
        # Error handling
        bozo_error=parsed.bozo != 0,
        bozo_error_message=parsed.get("bozo_exception", None),
        # Feed content
        feed=__parse_feed(parsed),
    )


def __parse_feed(parsed: feedparser.FeedParserDict) -> Feed:
    feed = parsed.feed

    return Feed(
        # Identification
        id=feed.get("id"),
        title=feed.get("title"),
        title_detail=(
            __create_info_details(feed.get("title_detail"))
            if feed.get("title_detail")
            else None
        ),
        subtitle=feed.get("subtitle"),
        subtitle_detail=(
            __create_info_details(feed.get("subtitle_detail"))
            if feed.get("subtitle_detail")
            else None
        ),
        # Links and media
        link=feed.get("link"),
        links=[__create_link(link) for link in feed.get("links", [])],
        icon=feed.get("icon"),
        image=__create_image(feed.get("image")) if feed.get("image") else None,
        logo=feed.get("logo"),
        # Authorship
        author=feed.get("author"),
        author_detail=__create_author_detail(feed.get("author_detail")),
        contributors=[
            __must_create_author_detail(contributor)
            for contributor in feed.get("contributors", [])
        ],
        publisher=feed.get("publisher"),
        publisher_detail=(
            __create_author_detail(feed.get("publisher_detail"))
            if feed.get("publisher_detail")
            else None
        ),
        # Content metadata
        language=feed.get("language"),
        tags=[__create_tag(tag) for tag in feed.get("tags", [])],
        # Timestamps
        published=feed.get("published"),
        published_parsed=__parse_date(feed.get("published_parsed")),
        updated=feed.get("updated"),
        updated_parsed=__parse_date(feed.get("updated_parsed")),
        # Feed items
        items=[__parse_item(item) for item in parsed.get("entries", [])],
        # Additional information
        docs=feed.get("docs"),
        errorreportsto=feed.get("errorreportsto"),
        generator=feed.get("generator"),
        generator_detail=(
            GeneratorDetail(
                name=feed.get("generator_detail", {}).get("name"),
                href=feed.get("generator_detail", {}).get("href"),
                version=feed.get("generator_detail", {}).get("version"),
            )
            if feed.get("generator_detail")
            else None
        ),
        info=feed.get("info"),
        info_detail=(
            __create_info_details(feed.get("info_detail"))
            if feed.get("info_detail")
            else None
        ),
        # Rights and licensing
        license=feed.get("license"),
        rights=feed.get("rights"),
        rights_detail=(
            __create_info_details(feed.get("rights_detail"))
            if feed.get("rights_detail")
            else None
        ),
        # User input
        text_input=(
            TextInput(
                title=feed.get("text_input", {}).get("title"),
                link=feed.get("text_input", {}).get("link"),
                name=feed.get("text_input", {}).get("name"),
                description=feed.get("text_input", {}).get("description"),
            )
            if feed.get("text_input")
            else None
        ),
    )


def __parse_item(item: dict[Any, Any]) -> FeedItem:
    return FeedItem(
        # Identification
        id=item.get("id"),
        title=item.get("title"),
        title_detail=__create_info_details(item.get("title_detail")),
        # Links and media
        link=item.get("link"),
        links=[__create_link(link) for link in item.get("links", [])],
        enclosures=[
            __create_enclosure(enclosure) for enclosure in item.get("enclosures", [])
        ],
        # Content
        content=[
            Content(
                value=content.get("value"),
                type=content.get("type"),
                language=content.get("language"),
                base=content.get("base"),
            )
            for content in item.get("content", [])
        ],
        summary=item.get("summary"),
        summary_detail=__create_info_details(item.get("summary_detail")),
        # Authorship
        publisher=item.get("publisher"),
        publisher_detail=__create_author_detail(item.get("publisher_detail")),
        author=item.get("author"),
        author_detail=__create_author_detail(item.get("author_detail")),
        contributors=[
            __must_create_author_detail(contributor)
            for contributor in item.get("contributors", [])
        ],
        # Timestamps
        published=item.get("published"),
        published_parsed=__parse_date(item.get("published_parsed")),
        created=item.get("created"),
        created_parsed=__parse_date(item.get("created_parsed")),
        expired=item.get("expired"),
        expired_parsed=__parse_date(item.get("expired_parsed")),
        updated=item.get("updated"),
        updated_parsed=__parse_date(item.get("updated_parsed")),
        # Metadata
        source=__create_source(item.get("source")),
        tags=[__create_tag(tag) for tag in item.get("tags", [])],
        comments=item.get("comments"),
        # Rights
        license=item.get("license"),
    )


def __must_create_author_detail(author_detail: dict[str, Any]) -> AuthorDetail:
    return AuthorDetail(
        name=author_detail.get("name"),
        href=author_detail.get("href"),
        email=author_detail.get("email"),
    )


def __create_author_detail(
    author_detail: dict[str, Any] | None,
) -> AuthorDetail | None:
    if not author_detail:
        return None
    return __must_create_author_detail(author_detail)


def __create_image(image: dict) -> Image:
    return Image(
        title=image.get("title"),
        href=image.get("href"),
        link=image.get("link"),
        width=image.get("width"),
        height=image.get("height"),
        description=image.get("description"),
    )


def __create_link(link: dict) -> Link:
    return Link(
        rel=link.get("rel"),
        type=link.get("type"),
        href=link.get("href"),
        title=link.get("title"),
    )


def __create_info_details(details: dict[str, Any] | None) -> InfoDetails | None:
    if not details:
        return None
    return InfoDetails(
        value=details.get("value"),
        type=details.get("type"),
        language=details.get("language"),
        base=details.get("base"),
    )


def __create_tag(tag: dict) -> Tag:
    return Tag(
        term=tag.get("term"),
        scheme=tag.get("scheme"),
        label=tag.get("label"),
    )


def __parse_date(date: tuple | None) -> datetime | None:
    return datetime(*date[:6]) if date else None


def __create_source(source: dict[str, Any] | None) -> Source | None:
    if not source:
        return None

    return Source(
        # Identification
        id=source.get("id"),
        title=source.get("title"),
        title_detail=__create_info_details(source.get("title_detail")),
        subtitle=source.get("subtitle"),
        subtitle_detail=__create_info_details(source.get("subtitle_detail")),
        # Links and media
        link=source.get("link"),
        links=[__create_link(link) for link in source.get("links", [])],
        icon=source.get("icon"),
        logo=source.get("logo"),
        # Authorship
        author=source.get("author"),
        author_detail=__create_author_detail(source.get("author_detail")),
        contributors=[
            __must_create_author_detail(contributor)
            for contributor in source.get("contributors", [])
        ],
        # Content metadata
        language=source.get("language"),
        tags=[__create_tag(tag) for tag in source.get("tags", [])],
        # Timestamps
        published=source.get("published"),
        published_parsed=__parse_date(source.get("published_parsed")),
        updated=source.get("updated"),
        updated_parsed=__parse_date(source.get("updated_parsed")),
        # Rights
        rights=source.get("rights"),
        rights_detail=__create_info_details(source.get("rights_detail")),
    )


def __create_enclosure(enclosure: dict[str, Any]) -> Enclosure:
    length = enclosure.get("length")

    return Enclosure(
        href=enclosure.get("href"),
        length=int(length) if length and length.isdigit() else None,
        type=enclosure.get("type"),
    )
