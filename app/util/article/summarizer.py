from .types import OpenAIClient
from .util import ai_query_markdown
from app.helpers.types import Markdown
import logging

logger = logging.getLogger(__name__)


class Summarizer:
    def __init__(self, client: OpenAIClient):
        self.client = client

    async def shorten(self, article: Markdown) -> Markdown:
        logger.info("AI: Shortening and normalizing article")

        prompt = f"""
        You will receive a Markdown news/op-ed article (any language).

        ### OBJECTIVE
        Produce a **concise, neutral** version that preserves every *fact* while removing emotional colouring.

        ### PRIORITIES  (higher number wins in conflict)
        1. **Facts & data** — names, dates, numbers, places, events, direct statements of what happened.
        2. **Neutral tone** — no loaded adjectives/adverbs, value-judgements, rhetorical questions, exaggerations.
        3. **Brevity** — target final length ≤ 65 % of the original tokens.

        ### RULES
        - **Headline**
        • Extract main idea and rewrite it in a neutral form.
        • Output first, as `# Title`.

        - **Body**
        1. Keep headings (≤ H3), ordered/unordered lists, block quotes that convey facts.
        2. Trim or paraphrase block quotes **if** their only purpose is emotional emphasis ("vile", "disgrace" → remove).
        3. Delete redundancy, filler clichés, author self-references, political framing, unsupported opinions.
        4. Preserve all Markdown links.
            - If the original anchor text stays, keep the link exactly as [text](url).
            - If you paraphrase or drop the anchor text, the URL must still remain:
                - either attach it to a new, relevant word,
                - or place it right after the statement as (source) or [source](url).
            - Never delete or alter the URLs themselves.
        5. Do **not** add content or invent facts.
        6. Language must stay the same as the input.

        ### FORBIDDEN
        - Loaded words (e.g., _horrible, absurd, disgraceful, vile_), sarcastic asides, moral judgements, fear-mongering.
        - Summaries that drop concrete facts.
        - Adding new information.

        Return **only** the transformed article in Markdown.

        Markdown input:
        {article}
        """

        return await self.__query_ai(prompt)

    async def summarize(self, article: Markdown) -> Markdown:
        logger.info("AI: Shortening summary")

        prompt = f"""
        You will receive a news article (Markdown, any language).

        **Your task:** produce an *ultra-compact* summary **in the same language**.
        Return plain Markdown with:

        1. The original **headline** (exactly as given; keep any Markdown formatting).
        2. **5-7 short sentences** capturing who, what, when, where, why.
            - Do not exceed 7 sentences.
            - Preserve all key names, numbers, locations, dates, and outcomes.
            - List events chronologically if several occurred.
            - Retain relevant Markdown links, if present.

        **Do NOT** include quotes, background context, stylistic embellishments, sub-headings, or bullet points.

        Here is the article:
        {article}
        """

        return await self.__query_ai(prompt)

    async def __query_ai(self, prompt: str) -> str:
        return await ai_query_markdown(self.client, prompt)
