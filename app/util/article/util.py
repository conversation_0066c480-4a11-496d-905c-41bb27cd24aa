from bs4 import Tag
import re
from app.helpers.soup_helper import (
    new_soup,
    decompose_tags,
    extract_comments,
    remove_empty_tags,
    remove_intertag_whitespace,
)
from .types import OpenAIClient
from app.helpers.openai_helper import strip_markdown

UNWANTED_TAGS = [
    "script",
    "style",
    "noscript",
    "iframe",
    "svg",
    "form",
    "nav",
    "footer",
    "header",
    "aside",
    "img",
]


def clean_html(html: str) -> str:
    soup = new_soup(html)

    decompose_tags(soup, UNWANTED_TAGS)
    extract_comments(soup)

    remove_empty_tags(soup, except_tags=["br"])
    remove_intertag_whitespace(soup)

    for element in soup.find_all(True):
        if not isinstance(element, Tag):
            continue

        allowed_attrs = {}

        if element.name == "a" and element.has_attr("href"):
            allowed_attrs["href"] = element["href"]
        if element.name in ["td", "th"]:
            for attr in ["colspan", "rowspan"]:
                if element.has_attr(attr):
                    allowed_attrs[attr] = element[attr]

        element.attrs = allowed_attrs

    result = str(soup)

    return re.sub(r"\s+", " ", result)


async def ai_query_markdown(client: OpenAIClient, prompt: str) -> str:
    markdown = await client.query_as_user(prompt=prompt)
    return strip_markdown(markdown)
