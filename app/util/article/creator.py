from app.db import models
from app.helpers import soup_helper, markdown_helper, lang_helper
import hashlib

from .extractor import TrafilatureExtractor
from .util import clean_html
from app.helpers.types import Html, Markdown, LangCode
from .types import Content


def absolutize_links(html: Html, base_url: str) -> str:
    soup = soup_helper.new_soup(html)
    soup_helper.absolutize_links(soup, base_url)
    return str(soup)


def dedupe_key(html: Html) -> bytes:
    extractor = TrafilatureExtractor()
    normalized = (
        extractor.extract_precise(html)
        or extractor.extract_inexact(html)
        or clean_html(html)
    )
    return hashlib.sha256(normalized.encode()).digest()


def create(
    id: int,
    lang: LangCode | None,
    content: Content,
    short: Markdown,
    summary: Markdown | None,
) -> models.Article:
    original_text = markdown_helper.extract_text(content.markdown)
    short_text = markdown_helper.extract_text(short)
    summary_text = markdown_helper.extract_text(summary) if summary else short_text

    return models.Article(
        id=id,
        # stats
        original_length_chars=len(original_text),
        original_length_words=lang_helper.word_count(original_text),
        shorten_length_chars=len(short_text),
        shorten_length_words=lang_helper.word_count(short_text),
        summary_length_chars=len(summary_text),
        summary_length_words=lang_helper.word_count(summary_text),
        # flags
        has_long_version=summary is not None,
        is_extracted_by_ai=content.extracted_with_ai,
        cleaned_by_ai=content.cleaned_with_ai,
        # content
        title=markdown_helper.extract_title(short),
        summary=summary or short,
        language=lang,
    )
