import trafilatura
import markdownify
import logging
from .util import clean_html, ai_query_markdown
from .types import OpenAIClient, Content
from app.helpers.types import Markdown, Html

logger = logging.getLogger(__name__)

AI_STOP_WORD = "__NO_NEWS_54a1fd68-4869-4e56-bcc2-027f6ddf0f7d__"


class Extractor:
    def __init__(self, client: OpenAIClient):
        self.client = client
        self.trafilature_extractor = TrafilatureExtractor()
        self.ai_extractor = AiExtractor(client)

    async def extract(self, html: Html) -> Content | None:
        markdown = self.trafilature_extractor.extract_precise(html)
        if markdown:
            return Content(
                markdown=markdown, extracted_with_ai=False, cleaned_with_ai=False
            )

        markdown = self.trafilature_extractor.extract_inexact(html)
        extracted_with_ai = False

        if not markdown:
            logger.warning("AI: Extracting article from HTML")
            extracted_with_ai = True
            html = clean_html(html)
            markdown = await self.ai_extractor.extract(html)

        if AI_STOP_WORD in markdown:
            logger.warning("AI: No news found")
            return None

        logger.warning("AI: Removing metadata")
        markdown = await self.ai_extractor.remove_metadata(markdown)

        return Content(
            markdown=markdown,
            extracted_with_ai=extracted_with_ai,
            cleaned_with_ai=True,
        )


class TrafilatureExtractor:
    def extract_precise(self, html: Html) -> Markdown | None:
        return self.extract(html, favor_precision=True)

    def extract_inexact(self, html: Html) -> Markdown | None:
        return self.extract(html, favor_precision=False)

    def extract(self, html: Html, favor_precision: bool) -> Markdown | None:
        extracted = trafilatura.extract(
            html,
            output_format="html",
            include_links=True,
            fast=False,
            favor_precision=favor_precision,
            include_comments=False,
            deduplicate=True,
            include_formatting=True,
            with_metadata=False,
        )

        return self.__to_markdown(extracted) if extracted else None

    def __to_markdown(self, article: Html) -> Markdown:
        return markdownify.markdownify(
            article,
            heading_style="ATX",  # # Title
            default_title=True,
            autolinks=True,
            bullets="*",
            strip=["script", "style"],
        )


class AiExtractor:
    def __init__(self, client: OpenAIClient):
        self.client = client

    async def extract(self, html: Html) -> Markdown:
        prompt = f"""
        Here is an HTML page from a news website. Your task is to extract the main news article and present it in Markdown format:

        - Preserve headings, paragraphs, and lists.
        - Do not add anything beyond what is in the HTML.
        - If the article body is missing or there is no real news content, return exactly this word: `{AI_STOP_WORD}` (return nothing else, no explanation).

        Note: This HTML has already been cleaned. Tags like <article> or <main> may be missing.

        HTML content:
        ```html
        {html}
        """
        return await self.__query_ai(prompt)

    async def remove_metadata(self, article: Markdown) -> Markdown:
        prompt = f"""
        You will receive a Markdown-formatted article.

        Your task is to remove any non-essential metadata from the top or bottom of the article, including:
        - author names (e.g. "Deutsche Welle"),
        - publication dates,
        - image captions,
        - photographer credits,
        - comment links,
        - tags, keywords, or section info.

        Keep only the actual article text, including headlines, paragraphs, and bullet points.

        Do not reformat the content. Return cleaned Markdown only.

        Markdown:
        {article}
        """

        return await self.__query_ai(prompt)

    async def __query_ai(self, prompt: str) -> str:
        return await ai_query_markdown(self.client, prompt)
