from dataclasses import dataclass
from typing import Protocol, runtime_checkable, Optional
from app.helpers.types import Markdown


@dataclass(frozen=True)
class UsageInfo:
    """Standardized usage information across all AI providers"""

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_usd: float
    model: str
    provider: str


@runtime_checkable
class OpenAIClient(Protocol):
    async def query_as_user(self, prompt: str) -> str:
        pass

    def get_model_name(self) -> str:
        """Return the model name used by this client"""
        pass

    def get_provider_name(self) -> str:
        """Return the provider name (openai, deepseek, gemini)"""
        pass

    def get_last_usage(self) -> Optional[UsageInfo]:
        """Return usage information from the last query"""
        pass


@dataclass(frozen=True)
class Content:
    markdown: Markdown
    # debug info
    extracted_with_ai: bool
    cleaned_with_ai: bool
