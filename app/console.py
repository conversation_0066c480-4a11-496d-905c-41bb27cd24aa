import logging
from IPython import get_ipython
import httpx

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
)


http_client = httpx.AsyncClient(timeout=10.0, follow_redirects=True)


async def http_get(url: str) -> str:
    response = await http_client.get(url)
    response.raise_for_status()
    return response.text


async def get_article(url: str):
    from app.util.article import Extractor, Summarizer
    from app.util.article.types import OpenAIClient
    from app.boot import ai_client

    # Type annotation to help mypy understand the protocol
    client: OpenAIClient = ai_client  # type: ignore[assignment]

    summarizer = Summarizer(client)
    extractor = Extractor(client)

    html = await http_get(url)

    content = await extractor.extract(html)

    if not content:
        return None

    short = await summarizer.shorten(content.markdown)
    summary = await summarizer.summarize(short)

    return {"content": content, "short": short, "summary": summary}


ip = get_ipython()
ip.run_line_magic("load_ext", "autoreload")
ip.run_line_magic("autoreload", "2")
