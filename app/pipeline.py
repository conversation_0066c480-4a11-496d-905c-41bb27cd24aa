import procrastinate
import procrastinate.exceptions
import logging
from app.boot import (
    db_engine,
    procrastinator,
    s3_helper,
    new_snowflake_gen,
)
from app import news_maker
from app.db import feed_repo, models
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from app import rss_scrabber
from app.crawler import Crawler

logger = logging.getLogger(__name__)


async def check_rss_feeds():
    async with AsyncSession(bind=db_engine) as session:
        async for feed in feed_repo.read_all_to_check(session, now=datetime.now()):
            lock = f"feed_{feed.id}"
            task = check_rss_feed_task.configure(lock=lock, queueing_lock=lock)

            try:
                await task.defer_async(
                    feed_id=feed.id,
                    url=feed.url,
                    last_sha256=feed.last_downloaded_sha256,
                )
            except procrastinate.exceptions.AlreadyEnqueued:
                logger.warning(f"Task already enqueued for feed.id={feed.id}")


@procrastinator.task(
    name="rss_scrabber.check_rss_feed", queue="check_particular_rss_feed"
)
async def check_rss_feed_task(feed_id: int, url: str, last_sha256: str):
    items = await rss_scrabber.check_rss_feed(feed_id, url, last_sha256)

    news = news_maker.news_from_rss_list(new_snowflake_gen(), items)
    await news_maker.persist_news(db_engine, news)

    for item in items:
        if not item.link:
            continue
        task = download_one_task.configure(lock=f"feed_item_{item.id}")
        await task.defer_async(feed_item_id=item.id, url=item.link)


@procrastinator.task(
    name="page_scrabber.download_one",
    queue="download_web_page",
    retry=procrastinate.RetryStrategy(max_attempts=5, exponential_wait=60),
)
async def download_one_task(feed_item_id: int, url: str):
    crawler = Crawler(
        db_engine=db_engine,
        s3_client=s3_helper,
        id_generator=new_snowflake_gen(),
    )
    page = await crawler.get(url, ttl_seconds=60 * 60)

    async with AsyncSession(bind=db_engine) as session:
        item = await session.get_one(models.FeedItem, feed_item_id)
        item.web_page_id = page.id
        await session.commit()
