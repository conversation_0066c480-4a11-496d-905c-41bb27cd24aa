import random
from functools import cached_property
from urllib.parse import parse_qsl, urlenco<PERSON>, urlparse, urlunparse
from typing import NoReturn
from enum import Enum
from annotated_types import Annotated
from pydantic import Field, model_validator
from pydantic.alias_generators import to_snake
from pydantic_settings import BaseSettings


class AiProvider(str, Enum):
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"


def assert_never(value: NoReturn) -> NoReturn:
    """Helper function for exhaustive type checking.

    This function should never be called at runtime - it's used to ensure
    that all cases in a union type are handled in match/case statements.
    """
    raise AssertionError(f"Unhandled case: {value}")


class Settings(BaseSettings):
    raw_database_url: Annotated[str, Field(validation_alias="DATABASE_URL")]

    s3_region: Annotated[str, Field(validation_alias="S3_REGION")]
    s3_endpoint_url: Annotated[str, Field(validation_alias="S3_ENDPOINT_URL")]
    s3_access_key_id: Annotated[str, Field(validation_alias="S3_ACCESS_KEY_ID")]
    s3_secret_access_key: Annotated[str, Field(validation_alias="S3_SECRET_ACCESS_KEY")]
    s3_bucket_name: Annotated[str, Field(validation_alias="S3_BUCKET_NAME")]

    openai_api_key: Annotated[
        str | None, Field(validation_alias="OPENAI_API_KEY", default=None)
    ]

    # DeepSeek configuration (optional)
    deepseek_api_key: Annotated[
        str | None, Field(validation_alias="DEEPSEEK_API_KEY", default=None)
    ]
    deepseek_base_url: Annotated[
        str,
        Field(
            validation_alias="DEEPSEEK_BASE_URL", default="https://api.deepseek.com/v1"
        ),
    ]

    # Gemini configuration (optional)
    gemini_api_key: Annotated[
        str | None, Field(validation_alias="GEMINI_API_KEY", default=None)
    ]
    gemini_model: Annotated[
        str, Field(validation_alias="GEMINI_MODEL", default="gemini-2.0-flash-exp")
    ]

    ai_provider: Annotated[
        AiProvider, Field(validation_alias="AI_PROVIDER", default=AiProvider.OPENAI)
    ]

    create_s3_bucket: Annotated[
        bool, Field(validation_alias="CREATE_S3_BUCKET", default=False)
    ]

    @model_validator(mode="after")
    def validate_ai_provider_keys(self):
        """Validate that required API keys are present based on AI_PROVIDER setting"""
        match self.ai_provider:
            case AiProvider.OPENAI:
                if not self.openai_api_key:
                    raise ValueError(
                        "OPENAI_API_KEY is required when AI_PROVIDER=openai"
                    )
            case AiProvider.DEEPSEEK:
                if not self.deepseek_api_key:
                    raise ValueError(
                        "DEEPSEEK_API_KEY is required when AI_PROVIDER=deepseek"
                    )
            case AiProvider.GEMINI:
                if not self.gemini_api_key:
                    raise ValueError(
                        "GEMINI_API_KEY is required when AI_PROVIDER=gemini"
                    )
            case _:
                assert_never(self.ai_provider)
        return self

    @property
    def database_url_async(self) -> str:
        url = self.database_url_sync

        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://", 1)

        return url

    @property
    def database_url_sync(self) -> str:
        url = self.raw_database_url

        parsed = urlparse(url)
        query_params = dict(parse_qsl(parsed.query))
        query_params.pop("sslmode", None)  # Remove unsupported param

        return urlunparse(parsed._replace(query=urlencode(query_params)))

    @cached_property
    def snowflake_id(self) -> int:
        return random.randint(0, 1023)

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "alias_generator": to_snake,
        "populate_by_name": True,
        "extra": "allow",
    }
