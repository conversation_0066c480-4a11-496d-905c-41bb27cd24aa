from dataclasses import dataclass
from app.clients import s3
from app.db import models, news_repo
from app.util import article
from app.helpers.types import Html, LangCode, Markdown
from app.helpers import lang_helper
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
import asyncio
import gzip


# TODO
# - GZIP html on S3?
# - Dedupe with creator.dedupe_key
# - Easier language detection (examine language from Trafilatura first, then from the past stats, then from RSS metadata)
# - Pre-instantiate and share lang-detector (needs 2GiGs or RAM)


class PageSizeError(Exception):
    pass


class ContentExtractionError(Exception):
    pass


@dataclass(frozen=True)
class ArticleOptions:
    article_id: int
    html_size_min: int
    html_size_max: int


class ArticleMaker:
    def __init__(
        self,
        options: ArticleOptions,
        db_engine: AsyncEngine,
        s3_client: s3.S3,
        extractor: article.Extractor,
        summarizer: article.Summarizer,
    ):
        self.options = options
        self.db_engine = db_engine
        self.s3_client = s3_client
        self.extractor = extractor
        self.summarizer = summarizer

    async def make_from_rss(self, feed_item_id: int) -> None:
        async with AsyncSession(bind=self.db_engine) as session:
            feed_item = await session.get_one(models.FeedItem, feed_item_id)
            feed = await session.get_one(models.Feed, feed_item.feed_id)
            if not feed.cr_articles:
                return

            if await self.__is_article_already_created(session, feed_item_id):
                return

            page = await session.get_one(models.Page, feed_item.web_page_id)
            self.__check_page_size(page.size)

        html = await self.__download_html(page.sha256)
        html = article.absolutize_links(html, feed.url)

        content = await self.extractor.extract(html)
        if not content:
            raise ContentExtractionError(
                f"Failed to extract content from page_id={page.id}, page_url={page.url}"
            )

        short_md = await self.summarizer.shorten(content.markdown)

        detect_lang_task = asyncio.to_thread(self.__detect_language_sync, short_md)
        summarize_task = self.summarizer.summarize(short_md)

        language = await detect_lang_task
        summary_md = await summarize_task

        article_record = article.create(
            id=self.options.article_id,
            lang=language,
            content=content,
            short=short_md,
            summary=summary_md,
        )

        await self.__upload_article(
            id=article_record.id,
            short=short_md,
            original=content.markdown,
        )

        await self.__save_article_record(article_record)

    async def __is_article_already_created(
        self, session: AsyncSession, feed_item_id: int
    ) -> bool:
        news_item = await news_repo.get_by_type(
            session, models.SourceTypeEnum.RSS, feed_item_id
        )
        return news_item.article_id is not None

    async def __download_html(self, page_sha256: bytes) -> Html:
        html_s3_key = s3.page_content_key(page_sha256)
        html_bytes = await asyncio.to_thread(self.s3_client.get_sync, html_s3_key)
        return html_bytes.decode("utf-8")

    def __check_page_size(self, size: int):
        if size < self.options.html_size_min:
            raise PageSizeError(f"Page too small: {size}")
        if size > self.options.html_size_max:
            raise PageSizeError(f"Page too large: {size}")

    def __detect_language_sync(self, text: str) -> LangCode | None:
        language_detector = lang_helper.LanguageDetector()
        return language_detector.detect_iso_639_1(text)

    async def __upload_article(self, id: int, short: Markdown, original: Markdown):
        async def upload(key: str, text: Markdown):
            content = gzip.compress(text.encode("utf-8"))
            extra_args = {"ContentType": "text/markdown", "ContentEncoding": "gzip"}
            return await asyncio.to_thread(
                lambda: self.s3_client.upload_sync(
                    key=key, content=content, extra_args=extra_args
                )
            )

        await asyncio.gather(
            upload(s3.article_original_key(id), original),
            upload(s3.article_short_key(id), short),
        )

    async def __save_article_record(self, record: models.Article):
        async with AsyncSession(bind=self.db_engine) as session:
            session.add(record)
            await session.commit()
