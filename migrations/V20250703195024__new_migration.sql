CREATE TYPE public.source_type_enum AS ENUM ('rss', 'web', 'telegram');

CREATE TABLE public.news (
    id BIGINT PRIMARY KEY,
    source_id BIGINT NOT NULL,
    article_id BIGINT REFERENCES public.articles(id) ON DELETE RESTRICT,
    published_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    source_type public.source_type_enum NOT NULL,
    title TEXT NOT NULL CHECK (char_length(title) <= 1024),
    original_url TEXT NOT NULL CHECK (char_length(original_url) <= 1024),
    CONSTRAINT news_unique_source_type_id UNIQUE (source_type, source_id)
);

CREATE INDEX news_article_id_idx ON public.news(article_id);
