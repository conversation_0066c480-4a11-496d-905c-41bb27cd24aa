CREATE TABLE public.articles (
    id BIGINT PRIMARY KEY,

    original_length_chars SMALLINT NOT NULL CHECK (original_length_chars >= 0),
    original_length_words SMALLINT NOT NULL CHECK (original_length_words >= 0),
    shorten_length_chars SMALLINT NOT NULL CHECK (shorten_length_chars >= 0),
    shorten_length_words SMALLINT NOT NULL CHECK (shorten_length_words >= 0),
    summary_length_chars SMALLINT NOT NULL CHECK (summary_length_chars >= 0),
    summary_length_words SMALLINT NOT NULL CHECK (summary_length_words >= 0),

    has_long_version BOOLEAN NOT NULL,
    is_extracted_by_ai BOOLEAN NOT NULL,
    cleaned_by_ai BOOLEAN NOT NULL,

    created_at TIMESTAMPTZ NOT NULL,

    title TEXT NOT NULL CHECK (char_length(title) <= 1024),
    summary TEXT CHECK (char_length(summary) <= 2024),
    language TEXT CHECK (char_length(language) <= 10)
);
