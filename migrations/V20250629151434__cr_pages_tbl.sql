CREATE TABLE public.pages (
    id BIGINT PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    size BIGINT NOT NULL CHECK (size >= 0),
    sha256 BYTEA NOT NULL,
    url TEXT NOT NULL,
    CONSTRAINT page_url_check CHECK ((char_length(url) <= 1024)),
    CONSTRAINT page_sha256_check CHECK ((octet_length(sha256) = 32))
);

CREATE INDEX pages_url_idx ON public.pages(url);