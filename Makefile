COMPOSE ?= docker-compose 

venv: 
	source .venv/bin/activate

install:
	pip install -r requirements.txt

dev-install:
	pip install -r requirements-dev.txt

format:
	black .

lint:
	ruff check . --fix

typecheck:
	mypy .

test:
	TESTCONTAINERS_RYUK_DISABLED=true pytest -vv -m "not docker"
	TESTCONTAINERS_RYUK_DISABLED=true pytest -vv -m "docker"

test-nodocker:
	TESTCONTAINERS_RYUK_DISABLED=true pytest -vv -m "not docker"

pre-commit: format lint typecheck test

lint-docker:
	@docker pull docker.io/hadolint/hadolint
	@docker run --rm -i hadolint/hadolint < Dockerfile

ci:
	@black --check .
	@ruff check .
	@mypy .
	@pytest

start-dev:
	fastapi dev app/main.py

python:
	docker run -it --rm python:3.13 bash

console:
	source .venv/bin/activate && IPYTHONDIR=. ipython -i app/console.py

create-migration:
	timestamp=$$(date -u +"%Y%m%d%H%M%S"); \
	filename="./migrations/V$${timestamp}__new_migration.sql"; \
	touch "$$filename"; \
	echo "Created migration file: $$filename"

migrate-dev:
	$(COMPOSE) --profile migrate run --rm flyway migrate

validate-env:
	python -c "from app.init import Settings; settings = Settings(); print('Environment validation passed')"
