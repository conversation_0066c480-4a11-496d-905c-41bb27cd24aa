from app.helpers.url_helper import remove_fragment, is_https


class TestRemoveFragment:
    def test_remove_fragment(self):
        url = "https://example.com/page#section"
        expected = "https://example.com/page"
        assert remove_fragment(url) == expected

    def test_no_fragment(self):
        url = "https://example.com/page"
        assert remove_fragment(url) == url

    def test_keep_query(self):
        url = "https://example.com/page?query=1#fragment"
        expected = "https://example.com/page?query=1"
        assert remove_fragment(url) == expected

    def test_fragment_only(self):
        url = "https://example.com/#frag"
        expected = "https://example.com/"
        assert remove_fragment(url) == expected


class TestIsHttps:
    def test_https(self):
        assert is_https("https://example.com")

    def test_http(self):
        assert not is_https("http://example.com")

    def test_no_scheme(self):
        assert not is_https("example.com")

    def test_other_scheme(self):
        assert not is_https("ftp://example.com")
