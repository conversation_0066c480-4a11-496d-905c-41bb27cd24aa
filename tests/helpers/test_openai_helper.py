import pytest
from types import SimpleNamespace
from app.helpers.openai_helper import cost_usd, strip_markdown


class TestCostUsd:
    def test_basic(self):
        usage = SimpleNamespace(prompt_tokens=1000, completion_tokens=500)
        expected_cost = (1000 * 0.005 / 1000) + (500 * 0.02 / 1000)
        assert cost_usd(usage) == expected_cost

    def test_zero_tokens(self):
        usage = SimpleNamespace(prompt_tokens=0, completion_tokens=0)
        assert cost_usd(usage) == 0.0

    def test_only_prompt(self):
        usage = SimpleNamespace(prompt_tokens=200, completion_tokens=0)
        expected_cost = 200 * 0.005 / 1000
        assert cost_usd(usage) == expected_cost

    def test_only_completion(self):
        usage = SimpleNamespace(prompt_tokens=0, completion_tokens=300)
        expected_cost = 300 * 0.02 / 1000
        assert cost_usd(usage) == expected_cost


@pytest.mark.parametrize(
    "input_text, expected",
    [
        ("```markdown\nThis is text\n```", "This is text"),
        ("```markdown\n  content with spaces \n```", "content with spaces"),
        ("no markdown wrapper", "no markdown wrapper"),
        ("```markdown\n```", ""),  # empty content
        ("   ```markdown\ntrimmed\n```   ", "trimmed"),  # extra whitespace
    ],
)
def test_strip_markdown(input_text, expected):
    assert strip_markdown(input_text) == expected
