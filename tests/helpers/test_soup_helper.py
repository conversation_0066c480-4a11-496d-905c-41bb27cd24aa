import pytest
from bs4 import BeautifulSoup
from app.helpers.soup_helper import (
    new_soup,
    decompose_tags,
    extract_comments,
    remove_intertag_whitespace,
    remove_empty_tags,
    absolutize_links,
)


def test_new_soup_creates_soup():
    html = "<p>Hello</p>"
    soup = new_soup(html)
    assert isinstance(soup, BeautifulSoup)
    assert soup.p.text == "Hello"


def test_decompose_tags_removes_specified_tags():
    soup = new_soup("<div><script>bad</script><p>ok</p></div>")
    decompose_tags(soup, ["script"])
    assert soup.find("script") is None
    assert soup.find("p") is not None


def test_extract_comments_removes_html_comments():
    soup = new_soup("<div>Hello<!--secret--></div>")
    extract_comments(soup)
    assert "<!--" not in str(soup)


def test_remove_intertag_whitespace_removes_only_whitespace_nodes():
    soup = new_soup("<div>\n  <p>Text</p>   <span>More</span>\n</div>")
    remove_intertag_whitespace(soup)
    assert "\n" not in soup.text
    assert soup.p is not None and soup.span is not None


def test_remove_empty_tags_removes_empty_but_not_specified():
    html = "<div><span></span><p> </p><b>keep</b><i></i></div>"
    soup = new_soup(html)
    remove_empty_tags(soup, except_tags=["p"])
    assert soup.find("span") is None
    assert soup.find("i") is None
    assert soup.find("p") is not None  # because it's in except_tags
    assert soup.find("b") is not None


class TestAbsolutizeLinks:
    @pytest.mark.parametrize(
        "html_snippet, base, tag, attr, expected",
        [
            # root-relative
            (
                '<a href="/features">Link</a>',
                "https://example.com/blog/post/",
                "a",
                "href",
                "https://example.com/features",
            ),
            # root-relative 2
            (
                '<a href="/features">Link</a>',
                "https://example.com/blog/index.html",
                "a",
                "href",
                "https://example.com/features",
            ),
            # page-relative
            (
                '<img src="images/logo.png"/>',
                "https://example.com/blog/post/",
                "img",
                "src",
                "https://example.com/blog/post/images/logo.png",
            ),
            # page-relative 2
            (
                '<img src="images/logo.png"/>',
                "https://example.com/blog/index.html",
                "img",
                "src",
                "https://example.com/blog/images/logo.png",
            ),
            # fragment-only
            (
                '<a href="#comments">Jump</a>',
                "https://example.com/blog/post/",
                "a",
                "href",
                "https://example.com/blog/post/#comments",
            ),
            # fragment-only 2
            (
                '<a href="#comments">Jump</a>',
                "https://example.com/blog/index.html",
                "a",
                "href",
                "https://example.com/blog/index.html#comments",
            ),
        ],
    )
    def test_relative_links_are_absolutized(
        self, html_snippet, base, tag, attr, expected
    ):
        soup = BeautifulSoup(html_snippet, "html.parser")

        assert absolutize_links(soup, base) is None
        assert soup.find(tag)[attr] == expected

    @pytest.mark.parametrize(
        "html_snippet, tag, attr",
        [
            ('<script src="https://cdn.example.com/app.js"></script>', "script", "src"),
            ('<a href="mailto:<EMAIL>">Mail</a>', "a", "href"),
            ('<img src="data:image/png;base64,AAAA"/>', "img", "src"),
            ('<a href="tel:+123456789">Call</a>', "a", "href"),
            ('<video src="//static.example.com/vid.mp4"></video>', "video", "src"),
        ],
    )
    def test_absolute_and_special_are_preserved(self, html_snippet, tag, attr):
        soup = BeautifulSoup(html_snippet, "html.parser")
        original = soup.find(tag)[attr]

        absolutize_links(soup, "https://example.com/anything/")
        assert soup.find(tag)[attr] == original

    def test_navigablestrings_are_ignored(self):
        html = """
            Some text
            <a href="/article">Article</a>
            More text
        """
        soup = BeautifulSoup(html, "html.parser")

        absolutize_links(soup, "https://example.com/")
        assert "Some text" in str(soup)
