import pytest
from app.helpers.lang_helper import LanguageDetector, word_count


class TestLanguageDetector:
    @pytest.fixture(autouse=True)
    def setup_detector(self):
        self.detector = LanguageDetector()

    @pytest.mark.parametrize(
        "text, expected",
        [
            ("Hello world, how are you today?", "en"),
            ("What a beautiful morning!", "en"),
            # Mandarin Chinese
            ("你好世界，今天过得怎么样？", "zh"),
            ("我很喜欢这个天气", "zh"),
            # Hindi
            ("नमस्ते दुनिया, आज कैसे हैं आप?", "hi"),
            ("मौसम बहुत अच्छा है", "hi"),
            # Spanish
            ("Hola mundo, ¿cómo estás hoy?", "es"),
            ("¡Qué hermoso día hace!", "es"),
            # Arabic
            ("مرحبا بالعالم، كيف حالك اليوم؟", "ar"),
            ("الطقس جميل اليوم", "ar"),
            # Bengali
            ("ওহে বিশ্ব, আজ কেমন আছো?", "bn"),
            ("আজ আবহাওয়া খুব ভালো", "bn"),
            # Portuguese
            ("Ol<PERSON> mundo, como você está hoje?", "pt"),
            ("Que dia maravilhoso!", "pt"),
            # Russian
            ("Привет мир, как дела сегодня?", "ru"),
            ("Какая прекрасная погода!", "ru"),
            # Japanese
            ("こんにちは世界、今日はお元気ですか？", "ja"),
            ("今日はとても良い天気ですね", "ja"),
            # French
            ("Bonjour le monde, comment allez-vous?", "fr"),
            ("Quelle belle journée!", "fr"),
            # German
            ("Hallo Welt, wie geht es dir heute?", "de"),
            ("Was für ein schöner Tag!", "de"),
            # Indonesian
            ("Halo dunia, apa kabar hari ini?", "id"),
            ("Cuaca hari ini sangat bagus!", "id"),
            # Korean
            ("안녕하세요 세상, 오늘 어떠세요?", "ko"),
            ("오늘 날씨가 정말 좋네요", "ko"),
            # Turkish
            ("Merhaba dünya, bugün nasılsın?", "tr"),
            ("Ne güzel bir gün!", "tr"),
            # Vietnamese
            ("Xin chào thế giới, hôm nay bạn thế nào?", "vi"),
            ("Thời tiết hôm nay thật đẹp!", "vi"),
            # Italian
            ("Ciao mondo, come stai oggi?", "it"),
            ("Che bella giornata!", "it"),
            # Thai
            ("สวัสดีชาวโลก วันนี้เป็นอย่างไรบ้าง?", "th"),
            ("อากาศวันนี้ดีจังเลย", "th"),
            # Polish
            ("Witaj świecie, jak się masz dzisiaj?", "pl"),
            ("Co za piękny dzień!", "pl"),
            # Urdu
            ("ہیلو دنیا، آج آپ کیسے ہیں؟", "ur"),
            ("آج موسم کتنا خوبصورت ہے", "ur"),
            # Ukrainian
            ("Привіт світ, як справи сьогодні?", "uk"),
            ("Мені подобається українська мова", "uk"),
            # Estonian
            ("Tere maailm, kuidas sul läheb?", "et"),
            ("Täna on ilus päev!", "et"),
        ],
    )
    def test_detect_language(self, text: str, expected: str):
        assert self.detector.detect_iso_639_1(text) == expected

    def test_detect_language_empty(self):
        assert self.detector.detect_iso_639_1("  ") is None


class TestWordCount:
    @pytest.mark.parametrize(
        "text, expected",
        [
            ("", 0),
            ("hello world", 2),
            ("one   two three", 3),
            ("  spaced   out   text  ", 3),
            ("line1\nline2 line3", 3),
        ],
    )
    def test_basic(self, text: str, expected: int):
        assert word_count(text) == expected
