import pytest
from app.helpers.markdown_helper import extract_text, extract_title


class TestExtractText:
    @pytest.mark.parametrize(
        "md, expected",
        [
            (
                "This is a [link](https://example.com) and **bold** text.",
                "This is a link and bold text.",
            ),
            ("- item one\n- item two", "item one item two"),
            ("# Title\n\nParagraph here.", "Title Paragraph here."),
            ("hello\u00a0world", "hello world"),
        ],
    )
    def test_basic(self, md: str, expected: str):
        assert extract_text(md) == expected

    def test_collapse_whitespace(self):
        md = "Word    Word\n\t\nWord"
        assert extract_text(md) == "Word Word Word"

    def test_simple_unordered_list(self):
        md = "* apple\n* banana\n* cherry"
        assert extract_text(md) == "apple banana cherry"


class TestExtractTitle:
    def test_header_used(self):
        md = "# My Title\n\nParagraph"
        assert extract_title(md) == "My Title"

    def test_fallback_first_sentence(self):
        md = "No header here. Second sentence."
        assert extract_title(md) == "No header here."

    def test_truncation(self):
        md = "# This is a very long header that should be truncated"
        assert extract_title(md, limit=10) == "This is a..."
