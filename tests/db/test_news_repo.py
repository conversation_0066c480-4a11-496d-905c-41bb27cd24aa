import pytest
from datetime import datetime, UTC
from sqlalchemy.exc import NoResultFound

from app.db.models import News, SourceTypeEnum
from app.db.news_repo import get_by_type

pytestmark = pytest.mark.docker


@pytest.mark.asyncio
class TestGetByType:
    async def test_returns_matching_row(self, async_session, id_generator):
        news_id = next(id_generator)
        source_id = next(id_generator)
        published_at = datetime.now(UTC)

        news = News(
            id=news_id,
            source_type=SourceTypeEnum.RSS,
            source_id=source_id,
            published_at=published_at,
            title="Test",
            original_url="https://example.com",
        )
        async_session.add(news)
        await async_session.commit()

        result = await get_by_type(async_session, SourceTypeEnum.RSS, source_id)

        assert result.id == news_id
        assert result.source_type == SourceTypeEnum.RSS
        assert result.source_id == source_id
        assert result.published_at == published_at
        assert result.title == "Test"
        assert result.original_url == "https://example.com"

    async def test_raises_when_missing(self, async_session, id_generator):
        with pytest.raises(NoResultFound):
            await get_by_type(async_session, SourceTypeEnum.RSS, next(id_generator))
