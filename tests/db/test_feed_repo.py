import pytest
from datetime import datetime, timedelta, UTC
from app.db.models import Feed
from app.db import feed_repo

pytestmark = pytest.mark.docker


@pytest.mark.asyncio
class TestReadAllToCheck:
    async def test_filters_disabled_feeds(self, async_session, id_generator):
        now = datetime.now(UTC)
        threshold = now - timedelta(seconds=feed_repo.READ_INTERVAL_SECONDS)
        enabled_id = next(id_generator)
        disabled_id = next(id_generator)

        enabled_feed = Feed(
            id=enabled_id,
            url=f"https://example.com/{next(id_generator)}",
            last_downloaded_at=threshold - timedelta(seconds=1),
            enabled=True,
        )
        disabled_feed = Feed(
            id=disabled_id,
            url=f"https://example.com/{next(id_generator)}",
            last_downloaded_at=threshold - timedelta(seconds=1),
            enabled=False,
        )
        async_session.add_all([enabled_feed, disabled_feed])
        await async_session.commit()

        result_ids = [
            f.id async for f in feed_repo.read_all_to_check(async_session, now)
        ]

        assert enabled_id in result_ids
        assert disabled_id not in result_ids

    async def test_filters_recent_and_error_feeds(self, async_session, id_generator):
        now = datetime.now(UTC)
        threshold = now - timedelta(seconds=feed_repo.READ_INTERVAL_SECONDS)
        old_ok_id = next(id_generator)
        too_recent_id = next(id_generator)
        too_many_errors_id = next(id_generator)

        old_ok = Feed(
            id=old_ok_id,
            url=f"https://example.com/{next(id_generator)}",
            last_downloaded_at=threshold - timedelta(seconds=1),
            consec_err_cnt=1,
        )
        too_recent = Feed(
            id=too_recent_id,
            url=f"https://example.com/{next(id_generator)}",
            last_downloaded_at=threshold + timedelta(seconds=1),
        )
        too_many_errors = Feed(
            id=too_many_errors_id,
            url=f"https://example.com/{next(id_generator)}",
            last_downloaded_at=threshold - timedelta(seconds=1),
            consec_err_cnt=feed_repo.MAX_CONSEC_ERR_CNT,
        )
        async_session.add_all([old_ok, too_recent, too_many_errors])
        await async_session.commit()

        result_ids = [
            f.id async for f in feed_repo.read_all_to_check(async_session, now)
        ]

        assert old_ok_id in result_ids
        assert too_recent_id not in result_ids
        assert too_many_errors_id not in result_ids
