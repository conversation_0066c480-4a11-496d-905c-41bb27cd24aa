from datetime import datetime, timedelta, UTC
from app.db.models import Page
from app.db.page_repo import find_by_url
import pytest

pytestmark = pytest.mark.docker


@pytest.mark.asyncio
class TestFindByUrl:
    async def test_create_and_fetch_by_id(self, async_session, id_generator):
        """Round-trip: insert one row and read it back by id."""
        url = f"https://example.com/{next(id_generator)}"
        now = datetime.now(UTC)

        original = self.__new_page(next(id_generator), url, now)
        expected = original.model_copy(deep=True)

        async_session.add(original)
        await async_session.commit()

        retrieved = await async_session.get(Page, expected.id)

        assert retrieved is not None
        assert retrieved.id == expected.id
        assert retrieved.url == expected.url
        assert retrieved.created_at == expected.created_at
        assert retrieved.size == expected.size
        assert retrieved.sha256 == expected.sha256

    async def test_returns_most_recent_match(self, async_session, id_generator):
        """When several rows match, the newest (highest id) is returned."""
        url = f"https://example.com/{next(id_generator)}"
        now = datetime.now(UTC)

        older = self.__new_page(next(id_generator), url, now - timedelta(hours=2))
        newer = self.__new_page(next(id_generator), url, now - timedelta(hours=1))

        async_session.add_all([older, newer])
        await async_session.commit()

        since = now - timedelta(days=1)
        result = await find_by_url(async_session, url, since)

        assert result is not None
        assert result.id == newer.id
        assert result.created_at == newer.created_at

    async def test_respects_since_cutoff(self, async_session, id_generator):
        """Rows older than *since* are ignored."""
        url = f"https://example.com/{next(id_generator)}"
        now = datetime.now(UTC)

        too_old = self.__new_page(next(id_generator), url, now - timedelta(days=2))
        async_session.add(too_old)
        await async_session.commit()

        since = now - timedelta(hours=1)
        result = await find_by_url(async_session, url, since)

        assert result is None

    async def test_returns_none_when_url_missing(self, async_session, id_generator):
        """Returns None when no rows for that URL exist at all."""
        since = datetime.now(UTC) - timedelta(days=7)
        result = await find_by_url(
            async_session,
            f"https://example.com/{next(id_generator)}",
            since,
        )
        assert result is None

    def __new_page(self, id: int, url: str, created_at: datetime) -> Page:
        page = Page(id=id, url=url, created_at=created_at)
        page.update_with_content(b"content")
        return page
