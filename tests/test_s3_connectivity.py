import pytest

pytestmark = pytest.mark.docker


def test_put_and_get_object(s3_boto3_client):
    bucket = "tests3connectivity"
    key = "hello.txt"
    body = b"Hello MinIO!"

    try:
        s3_boto3_client.create_bucket(Bucket=bucket)

        s3_boto3_client.put_object(Bucket=bucket, Key=key, Body=body)

        obj = s3_boto3_client.get_object(Bucket=bucket, Key=key)
        data = obj["Body"].read()

        assert data == body
    finally:
        s3_boto3_client.delete_object(Bucket=bucket, Key=key)
