import hashlib
from datetime import datetime


# ✏️ Adjust the import below so that it points to *your* package.
# For example, if your package root is "rss_reader", use:
# from rss_reader.parser import parse
from app.util.rss import parse


ATOM_FEED = """\
<?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <title>Example Feed</title>
  <subtitle>A subtitle.</subtitle>

  <!-- self-link first so that feed.link ends up being the *site* link -->
  <link href="http://example.org/feed/" rel="self"/>
  <link href="http://example.org/"/>

  <updated>2003-12-13T18:30:02Z</updated>
  <id>urn:uuid:60a76c80-d399-11d9-b93C-0003939e0af6</id>

  <author>
    <name><PERSON></name>
  </author>

  <entry>
    <title>Atom-Powered Robots Run Amok</title>
    <link href="http://example.org/2003/12/13/atom03"/>
    <id>urn:uuid:1225c695-cfb8-4ebb-aaaa-80da344efa6a</id>
    <updated>2003-12-13T18:30:02Z</updated>
    <summary>Some text.</summary>
  </entry>
</feed>
"""


def test_parse_atom_feed():
    response = parse(ATOM_FEED)

    # --- basic response sanity ------------------------------------------------
    assert response.bozo_error is False
    assert response.encoding == "utf-8"
    assert response.version == "atom10"

    # --- feed-level fields ----------------------------------------------------
    feed = response.feed
    assert feed.title == "Example Feed"
    assert feed.subtitle == "A subtitle."
    assert feed.link == "http://example.org/"
    assert feed.updated_parsed == datetime(2003, 12, 13, 18, 30, 2)

    # --- item-level fields ----------------------------------------------------
    assert len(feed.items) == 1
    item = feed.items[0]

    assert item.title == "Atom-Powered Robots Run Amok"
    assert item.link == "http://example.org/2003/12/13/atom03"
    assert item.summary == "Some text."
    assert item.updated_parsed == datetime(2003, 12, 13, 18, 30, 2)

    # --- GUID + SHA-256 helper -----------------------------------------------
    expected_guid = "urn:uuid:1225c695-cfb8-4ebb-aaaa-80da344efa6a"
    assert item.guid == expected_guid

    expected_hash = hashlib.sha256(expected_guid.encode()).hexdigest()
    assert feed.items_sha256() == expected_hash


RSS_WITH_ENCLOSURE = """
<rss version="2.0">
  <channel>
    <title>Podcast</title>
    <item>
      <title>Episode 1</title>
      <enclosure url="http://example.com/ep1.mp3"
                 length="12345"
                 type="audio/mpeg"/>
      <guid>ep1</guid>
    </item>
  </channel>
</rss>
"""


def test_enclosure_parsing():
    feed = parse(RSS_WITH_ENCLOSURE).feed
    enclosure = feed.items[0].enclosures[0]

    assert enclosure.href == "http://example.com/ep1.mp3"
    assert enclosure.length == 12345
    assert enclosure.type == "audio/mpeg"


RSS20_FEED = """\
<rss version="2.0">
  <channel>
    <title>Example RSS</title>
    <link>http://example.com/</link>
    <description>Test feed</description>
    <pubDate>Mon, 21 Jun 2021 14:00:00 GMT</pubDate>

    <item>
      <title>First post</title>
      <link>http://example.com/post1</link>
      <guid>post1</guid>
      <pubDate>Mon, 21 Jun 2021 14:00:00 GMT</pubDate>
      <description>First entry</description>
    </item>

    <!-- second item omits <guid> to exercise guid-fallback-to-link -->
    <item>
      <title>Second post</title>
      <link>http://example.com/post2</link>
      <pubDate>Tue, 22 Jun 2021 15:30:00 GMT</pubDate>
      <description>Second entry</description>
    </item>
  </channel>
</rss>
"""


def test_parse_rss20_happy_path_and_hash_stability():
    resp = parse(RSS20_FEED)

    # ---- envelope -----------------------------------------------------------
    assert resp.bozo_error is False
    assert resp.version == "rss20"

    feed = resp.feed
    assert feed.title == "Example RSS"
    assert feed.link == "http://example.com/"

    # ---- items --------------------------------------------------------------
    assert len(feed.items) == 2
    first, second = feed.items

    # first item (has explicit guid)
    assert first.guid == "post1"
    assert first.published_parsed == datetime(2021, 6, 21, 14, 0, 0)

    # second item (no guid → link used)
    assert second.guid == "http://example.com/post2"
    assert second.published_parsed == datetime(2021, 6, 22, 15, 30, 0)

    # ---- items_sha256 should ignore ordering --------------------------------
    expected_hash = feed.items_sha256()

    # Reverse the list in-place; Feed is frozen, but its list is mutable
    feed.items.reverse()
    assert feed.items_sha256() == expected_hash

    # Manual hash check (for extra certainty)
    guids = sorted([first.guid, second.guid])
    manual_hash = hashlib.sha256("".join(guids).encode()).hexdigest()
    assert manual_hash == expected_hash


ATOM_LINK_TITLE = """\
<?xml version="1.0"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <title>Title</title>
  <link href="https://example.com" title="Example" />
</feed>
"""


def test_link_title_parsed_as_string():
    resp = parse(ATOM_LINK_TITLE)
    link = resp.feed.links[0]

    assert link.title == "Example"
    assert isinstance(link.title, str)
