import pytest
from app.util.article.creator import absolutize_links, dedupe_key, create
from app.util.article.types import Content


@pytest.mark.parametrize(
    "html,expected",
    [
        (
            '<a href="/relative">Relative</a>',
            '<a href="http://example.com/relative">Relative</a>',
        ),
        (
            '<a href="http://example.com/absolute">Absolute</a>',
            '<a href="http://example.com/absolute">Absolute</a>',
        ),
    ],
)
def test_absolutize_links(html, expected):
    result = absolutize_links(html, base_url="http://example.com")
    assert result == expected


def test_dedupe_key_ignores_style():
    html1 = "<p>Hello, world!</p>"
    html2 = "<p style='color: red;'>Hello, world!</p>"

    key1 = dedupe_key(html1)
    key2 = dedupe_key(html2)

    assert key1 == key2


class TestCreate:
    def test_creates_article_with_summary(self):
        # Given
        id = 1
        content = Content(
            markdown="# Title\n\nContent.",
            extracted_with_ai=False,
            cleaned_with_ai=False,
        )
        short = "# Short title\n\nShort content."
        summary = "Summary."

        # When
        article = create(id, "en", content, short, summary)

        # Then
        assert article.id == id
        # stats
        assert article.original_length_chars == 14
        assert article.original_length_words == 2
        assert article.shorten_length_chars == 26
        assert article.shorten_length_words == 4
        assert article.summary_length_chars == 8
        assert article.summary_length_words == 1
        # flags
        assert article.has_long_version
        assert not article.is_extracted_by_ai
        assert not article.cleaned_by_ai
        # content
        assert article.title == "Short title"
        assert article.summary == summary
        assert article.language == "en"

    def test_creates_article_without_summary(self):
        # Given
        id = 1
        content = Content(
            markdown="# Title\n\nContent.",
            extracted_with_ai=False,
            cleaned_with_ai=False,
        )
        short = "# Short title\n\nShort content."

        # When
        article = create(id, "en", content, short, None)

        # Then
        assert article.id == id
        # stats
        assert article.original_length_chars == 14
        assert article.original_length_words == 2
        assert article.shorten_length_chars == 26
        assert article.shorten_length_words == 4
        assert article.summary_length_chars == article.shorten_length_chars
        assert article.summary_length_words == article.shorten_length_words
        # flags
        assert not article.has_long_version
        assert not article.is_extracted_by_ai
        assert not article.cleaned_by_ai
        # content
        assert article.title == "Short title"
        assert article.summary == short
        assert article.language == "en"
