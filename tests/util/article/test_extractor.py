import pytest
from unittest.mock import AsyncMock
from app.util.article.extractor import Extractor


@pytest.mark.asyncio
async def test_extract_fallback_to_openai_client():
    html = "<html></html>"

    gpt_client = AsyncMock()
    gpt_client.query_as_user.side_effect = [
        "## AI-extracted article",
        "## AI-extracted article cleaned",
    ]

    extractor = Extractor(gpt_client)
    result = await extractor.extract(html)

    assert result.extracted_with_ai is True
    assert result.cleaned_with_ai is True
    assert result.markdown == "## AI-extracted article cleaned"


@pytest.mark.asyncio
async def test_extract_trafilatura_precise_success():
    html = """
    <html>
        <head><title>Test</title></head>
        <body><article><h1>Title</h1><p>Paragraph</p></article></body>
    </html>
    """

    gpt_client = AsyncMock()

    extractor = Extractor(gpt_client)
    result = await extractor.extract(html)

    assert result.extracted_with_ai is False
    assert result.cleaned_with_ai is False
    assert "# Title" in result.markdown
    gpt_client.query_as_user.assert_not_called()
