import pytest
from unittest.mock import AsyncMock
from app.util.article.summarizer import Summarizer


@pytest.mark.asyncio
async def test_shorten_calls_openai_with_correct_prompt():
    client = AsyncMock()
    client.query_as_user.return_value = "# Shortened title\n\nShortened content."

    summarizer = Summarizer(client)
    article = "# Original title\n\nEmotional fluff with a [link](https://example.com)."

    result = await summarizer.shorten(article)

    assert result == "# Shortened title\n\nShortened content."
    assert client.query_as_user.await_count == 1


@pytest.mark.asyncio
async def test_summarize_calls_openai_with_correct_prompt():
    client = AsyncMock()
    client.query_as_user.return_value = "# Title\n\n1. Summary sentence..."

    summarizer = Summarizer(client)
    article = "# Title\n\nSome markdown article text."

    result = await summarizer.summarize(article)

    assert result.startswith("# Title")
    assert "1." in result
    assert client.query_as_user.await_count == 1
