import pytest
from unittest.mock import AsyncMock, MagicMock
from openai.types.chat import Cha<PERSON><PERSON><PERSON>ple<PERSON>, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from openai.types import CompletionUsage
from app.clients.gpt import Client


class TestGPTClient:
    """Test OpenAI GPT client functionality"""

    @pytest.fixture
    def mock_openai_client(self):
        """Create a mock OpenAI client"""
        return AsyncMock()

    @pytest.fixture
    def gpt_client(self, mock_openai_client):
        """Create a GPT client with mocked OpenAI client"""
        return Client(mock_openai_client)

    @pytest.fixture
    def mock_chat_completion(self):
        """Create a mock ChatCompletion response"""
        usage = CompletionUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30)

        message = ChatCompletionMessage(
            content="Test response content", role="assistant"
        )

        choice = Choice(finish_reason="stop", index=0, message=message)

        return ChatCompletion(
            id="test-id",
            choices=[choice],
            created=1234567890,
            model="gpt-4",
            object="chat.completion",
            usage=usage,
        )

    @pytest.mark.asyncio
    async def test_query_as_user_success(
        self, gpt_client, mock_openai_client, mock_chat_completion
    ):
        """Test successful query_as_user call"""
        mock_openai_client.chat.completions.create.return_value = mock_chat_completion

        result = await gpt_client.query_as_user("Test prompt")

        assert result == "Test response content"
        mock_openai_client.chat.completions.create.assert_called_once_with(
            model="gpt-4o",
            messages=[{"role": "user", "content": "Test prompt"}],
            temperature=0,
        )

    @pytest.mark.asyncio
    async def test_query_as_user_no_choices(self, gpt_client, mock_openai_client):
        """Test query_as_user with no choices in response"""
        mock_response = ChatCompletion(
            id="test-id",
            choices=[],
            created=1234567890,
            model="gpt-4",
            object="chat.completion",
            usage=None,
        )
        mock_openai_client.chat.completions.create.return_value = mock_response

        with pytest.raises(ValueError, match="OpenAI returned no choices"):
            await gpt_client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_no_content(self, gpt_client, mock_openai_client):
        """Test query_as_user with no content in response"""
        message = ChatCompletionMessage(content=None, role="assistant")

        choice = Choice(finish_reason="stop", index=0, message=message)

        mock_response = ChatCompletion(
            id="test-id",
            choices=[choice],
            created=1234567890,
            model="gpt-4",
            object="chat.completion",
            usage=None,
        )
        mock_openai_client.chat.completions.create.return_value = mock_response

        with pytest.raises(ValueError, match="OpenAI returned no content"):
            await gpt_client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_with_usage_logging(
        self, gpt_client, mock_openai_client, mock_chat_completion
    ):
        """Test query_as_user logs usage information"""
        mock_openai_client.chat.completions.create.return_value = mock_chat_completion

        result = await gpt_client.query_as_user("Test prompt")

        assert result == "Test response content"
        # Test that the method completes successfully with usage info

    @pytest.mark.asyncio
    async def test_query_as_user_no_usage_info(self, gpt_client, mock_openai_client):
        """Test query_as_user handles missing usage information gracefully"""
        message = ChatCompletionMessage(content="Test response", role="assistant")

        choice = Choice(finish_reason="stop", index=0, message=message)

        mock_response = ChatCompletion(
            id="test-id",
            choices=[choice],
            created=1234567890,
            model="gpt-4",
            object="chat.completion",
            usage=None,
        )
        mock_openai_client.chat.completions.create.return_value = mock_response

        result = await gpt_client.query_as_user("Test prompt")
        assert result == "Test response"

    @pytest.mark.asyncio
    async def test_openai_api_error_propagation(self, gpt_client, mock_openai_client):
        """Test that OpenAI API errors are properly propagated"""
        # Create a generic exception to simulate API errors
        mock_openai_client.chat.completions.create.side_effect = Exception("API Error")

        with pytest.raises(Exception):
            await gpt_client.query_as_user("Test prompt")

    def test_client_initialization(self):
        """Test client initialization with OpenAI client"""
        mock_openai_client = MagicMock()
        client = Client(mock_openai_client)
        assert client.client == mock_openai_client
