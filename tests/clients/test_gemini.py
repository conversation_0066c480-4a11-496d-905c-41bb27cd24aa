import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import httpx
from app.clients.gemini import Client, GeminiClient


class TestGeminiClient:
    """Test Gemini API client functionality"""

    @pytest.fixture
    def gemini_client(self):
        """Create a Gemini client for testing"""
        return GeminiClient(
            api_key="test-api-key", model="gemini-2.0-flash-exp", timeout=30.0
        )

    @pytest.fixture
    def mock_gemini_response(self):
        """Create a mock Gemini API response"""
        return {
            "candidates": [{"content": {"parts": [{"text": "Test response content"}]}}],
            "usageMetadata": {
                "promptTokenCount": 20,
                "candidatesTokenCount": 30,
                "totalTokenCount": 50,
            },
        }

    @pytest.mark.asyncio
    async def test_generate_content_success(self, gemini_client, mock_gemini_response):
        """Test successful content generation"""
        with patch.object(gemini_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_gemini_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = await gemini_client.generate_content(
                prompt="Test prompt", temperature=0.7, max_tokens=2048
            )

            assert result == mock_gemini_response

            # Verify API call parameters
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert "gemini-2.0-flash-exp:generateContent" in call_args[0][0]

            # Check headers and params
            headers = call_args[1]["headers"]
            assert headers["Content-Type"] == "application/json"

            params = call_args[1]["params"]
            assert params["key"] == "test-api-key"

    @pytest.mark.asyncio
    async def test_generate_content_with_custom_model(
        self, gemini_client, mock_gemini_response
    ):
        """Test content generation with custom model"""
        with patch.object(gemini_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_gemini_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            await gemini_client.generate_content(
                prompt="Test prompt", model="gemini-1.5-pro", temperature=0.2
            )

            # Check that custom model was used in URL
            call_args = mock_post.call_args
            assert "gemini-1.5-pro:generateContent" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_generate_content_with_custom_parameters(
        self, gemini_client, mock_gemini_response
    ):
        """Test content generation with custom parameters"""
        with patch.object(gemini_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_gemini_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            await gemini_client.generate_content(
                prompt="Test prompt", temperature=0.3, max_tokens=1024
            )

            # Check payload
            call_args = mock_post.call_args
            payload = call_args[1]["json"]
            assert payload["contents"][0]["parts"][0]["text"] == "Test prompt"
            assert payload["generationConfig"]["temperature"] == 0.3
            assert payload["generationConfig"]["maxOutputTokens"] == 1024

    @pytest.mark.asyncio
    async def test_generate_content_http_error(self, gemini_client):
        """Test handling of HTTP errors"""
        with patch.object(gemini_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "API Error", request=MagicMock(), response=MagicMock()
            )
            mock_post.return_value = mock_response

            with pytest.raises(httpx.HTTPStatusError):
                await gemini_client.generate_content("Test prompt")

    @pytest.mark.asyncio
    async def test_client_close(self, gemini_client):
        """Test client close method"""
        with patch.object(gemini_client.http_client, "aclose") as mock_close:
            await gemini_client.close()
            mock_close.assert_called_once()

    def test_client_initialization(self):
        """Test Gemini client initialization"""
        client = GeminiClient(api_key="test-key", model="gemini-1.5-pro", timeout=45.0)
        assert client.api_key == "test-key"
        assert client.model == "gemini-1.5-pro"
        assert client.base_url == "https://generativelanguage.googleapis.com/v1beta"
        assert isinstance(client.http_client, httpx.AsyncClient)

    def test_client_default_initialization(self):
        """Test Gemini client with default parameters"""
        client = GeminiClient(api_key="test-key")
        assert client.api_key == "test-key"
        assert client.model == "gemini-2.0-flash-exp"


class TestGeminiClientWrapper:
    """Test Gemini client wrapper functionality"""

    @pytest.fixture
    def mock_gemini_client(self):
        """Create a mock Gemini client"""
        return AsyncMock()

    @pytest.fixture
    def client(self, mock_gemini_client):
        """Create a Gemini client wrapper with mocked client"""
        return Client(mock_gemini_client)

    @pytest.fixture
    def mock_gemini_response(self):
        """Create a mock Gemini API response"""
        return {
            "candidates": [{"content": {"parts": [{"text": "Test response content"}]}}],
            "usageMetadata": {
                "promptTokenCount": 20,
                "candidatesTokenCount": 30,
                "totalTokenCount": 50,
            },
        }

    @pytest.mark.asyncio
    async def test_query_as_user_success(
        self, client, mock_gemini_client, mock_gemini_response
    ):
        """Test successful query_as_user call"""
        mock_gemini_client.generate_content.return_value = mock_gemini_response

        result = await client.query_as_user("Test prompt")

        assert result == "Test response content"
        mock_gemini_client.generate_content.assert_called_once_with(
            prompt="Test prompt",
            temperature=0.0,
            max_tokens=4096,
        )

    @pytest.mark.asyncio
    async def test_query_as_user_no_candidates(self, client, mock_gemini_client):
        """Test query_as_user with no candidates in response"""
        mock_response = {"candidates": []}
        mock_gemini_client.generate_content.return_value = mock_response

        with pytest.raises(ValueError, match="Gemini returned no usable content"):
            await client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_malformed_response(self, client, mock_gemini_client):
        """Test query_as_user with malformed response"""
        mock_response = {"candidates": [{"content": {"parts": []}}]}
        mock_gemini_client.generate_content.return_value = mock_response

        with pytest.raises(ValueError, match="Gemini returned no usable content"):
            await client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_empty_response(self, client, mock_gemini_client):
        """Test query_as_user with empty response"""
        mock_gemini_client.generate_content.return_value = {}

        with pytest.raises(ValueError, match="Gemini returned empty response"):
            await client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_string_response_fallback(
        self, client, mock_gemini_client
    ):
        """Test query_as_user with string response fallback"""
        mock_gemini_client.generate_content.return_value = "Direct string response"

        result = await client.query_as_user("Test prompt")
        assert result == "Direct string response"

    @pytest.mark.asyncio
    async def test_query_as_user_dict_text_fallback(self, client, mock_gemini_client):
        """Test query_as_user with dict text fallback"""
        mock_response = {"text": "Fallback text response"}
        mock_gemini_client.generate_content.return_value = mock_response

        result = await client.query_as_user("Test prompt")
        assert result == "Fallback text response"

    @pytest.mark.asyncio
    async def test_query_as_user_dict_content_fallback(
        self, client, mock_gemini_client
    ):
        """Test query_as_user with dict content fallback"""
        mock_response = {"content": "Fallback content response"}
        mock_gemini_client.generate_content.return_value = mock_response

        result = await client.query_as_user("Test prompt")
        assert result == "Fallback content response"

    @pytest.mark.asyncio
    async def test_query_as_user_with_usage_logging(
        self, client, mock_gemini_client, mock_gemini_response
    ):
        """Test query_as_user logs usage information with cost calculation"""
        mock_gemini_client.generate_content.return_value = mock_gemini_response

        result = await client.query_as_user("Test prompt")

        assert result == "Test response content"
        # Test that the method completes successfully with usage info

    @pytest.mark.asyncio
    async def test_query_as_user_no_usage_info(self, client, mock_gemini_client):
        """Test query_as_user handles missing usage information gracefully"""
        mock_response = {
            "candidates": [{"content": {"parts": [{"text": "Test response content"}]}}]
        }
        mock_gemini_client.generate_content.return_value = mock_response

        result = await client.query_as_user("Test prompt")
        assert result == "Test response content"

    @pytest.mark.asyncio
    async def test_gemini_api_error_propagation(self, client, mock_gemini_client):
        """Test that Gemini API errors are properly propagated"""
        mock_gemini_client.generate_content.side_effect = httpx.HTTPStatusError(
            "API Error", request=MagicMock(), response=MagicMock()
        )

        with pytest.raises(ValueError, match="Gemini API error"):
            await client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_general_exception(self, client, mock_gemini_client):
        """Test handling of general exceptions"""
        mock_gemini_client.generate_content.side_effect = Exception("Unexpected error")

        with pytest.raises(ValueError, match="Gemini API error: Unexpected error"):
            await client.query_as_user("Test prompt")

    def test_wrapper_client_initialization(self):
        """Test wrapper client initialization"""
        mock_gemini_client = MagicMock()
        client = Client(mock_gemini_client)
        assert client.client == mock_gemini_client
