import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import httpx
from app.clients.deepseek import (
    Client,
    DeepSeekClient,
    ChatCompletion,
    Choice,
    Message,
    Usage,
)


class TestDeepSeekClient:
    """Test DeepSeek API client functionality"""

    @pytest.fixture
    def deepseek_client(self):
        """Create a DeepSeek client for testing"""
        return DeepSeekClient(
            api_key="test-api-key", base_url="https://api.deepseek.com/v1", timeout=30.0
        )

    @pytest.fixture
    def mock_http_response(self):
        """Create a mock HTTP response"""
        return {
            "choices": [
                {"message": {"role": "assistant", "content": "Test response content"}}
            ],
            "usage": {"prompt_tokens": 15, "completion_tokens": 25, "total_tokens": 40},
        }

    @pytest.mark.asyncio
    async def test_chat_completions_create_success(
        self, deepseek_client, mock_http_response
    ):
        """Test successful chat completion creation"""
        with patch.object(deepseek_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_http_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = await deepseek_client.chat_completions_create(
                model="deepseek-chat",
                messages=[{"role": "user", "content": "Test prompt"}],
                temperature=0.7,
            )

            assert isinstance(result, ChatCompletion)
            assert len(result.choices) == 1
            assert result.choices[0].message.content == "Test response content"
            assert result.choices[0].message.role == "assistant"
            assert result.usage.prompt_tokens == 15
            assert result.usage.completion_tokens == 25
            assert result.usage.total_tokens == 40

            # Verify API call parameters
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert call_args[0][0] == "https://api.deepseek.com/v1/chat/completions"

            # Check headers
            headers = call_args[1]["headers"]
            assert headers["Authorization"] == "Bearer test-api-key"
            assert headers["Content-Type"] == "application/json"

    @pytest.mark.asyncio
    async def test_chat_completions_create_with_custom_parameters(
        self, deepseek_client, mock_http_response
    ):
        """Test chat completion with custom parameters"""
        with patch.object(deepseek_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_http_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            await deepseek_client.chat_completions_create(
                model="deepseek-coder",
                messages=[{"role": "user", "content": "Write code"}],
                temperature=0.2,
                max_tokens=2048,
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.2,
                stream=False,
            )

            # Check that custom parameters were passed in payload
            call_args = mock_post.call_args
            payload = call_args[1]["json"]
            assert payload["model"] == "deepseek-coder"
            assert payload["temperature"] == 0.2
            assert payload["max_tokens"] == 2048
            assert payload["top_p"] == 0.9
            assert payload["frequency_penalty"] == 0.1
            assert payload["presence_penalty"] == 0.2
            assert not payload["stream"]

    @pytest.mark.asyncio
    async def test_chat_completions_create_http_error(self, deepseek_client):
        """Test handling of HTTP errors"""
        with patch.object(deepseek_client.http_client, "post") as mock_post:
            mock_response = MagicMock()
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "API Error", request=MagicMock(), response=MagicMock()
            )
            mock_post.return_value = mock_response

            with pytest.raises(httpx.HTTPStatusError):
                await deepseek_client.chat_completions_create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": "Test"}],
                )

    @pytest.mark.asyncio
    async def test_client_close(self, deepseek_client):
        """Test client close method"""
        with patch.object(deepseek_client.http_client, "aclose") as mock_close:
            await deepseek_client.close()
            mock_close.assert_called_once()

    def test_client_initialization(self):
        """Test DeepSeek client initialization"""
        client = DeepSeekClient(
            api_key="test-key", base_url="https://custom.deepseek.com/v1", timeout=45.0
        )
        assert client.api_key == "test-key"
        assert client.base_url == "https://custom.deepseek.com/v1"
        assert isinstance(client.http_client, httpx.AsyncClient)

    def test_client_default_initialization(self):
        """Test DeepSeek client with default parameters"""
        client = DeepSeekClient(api_key="test-key")
        assert client.api_key == "test-key"
        assert client.base_url == "https://api.deepseek.com/v1"


class TestDeepSeekClientWrapper:
    """Test DeepSeek client wrapper functionality"""

    @pytest.fixture
    def mock_deepseek_client(self):
        """Create a mock DeepSeek client"""
        return AsyncMock()

    @pytest.fixture
    def client(self, mock_deepseek_client):
        """Create a DeepSeek client wrapper with mocked client"""
        return Client(mock_deepseek_client)

    @pytest.fixture
    def mock_chat_completion(self):
        """Create a mock ChatCompletion response"""
        usage = Usage(prompt_tokens=15, completion_tokens=25, total_tokens=40)

        message = Message(role="assistant", content="Test response content")

        choice = Choice(message=message)

        return ChatCompletion(choices=[choice], usage=usage)

    @pytest.mark.asyncio
    async def test_query_as_user_success(
        self, client, mock_deepseek_client, mock_chat_completion
    ):
        """Test successful query_as_user call"""
        mock_deepseek_client.chat_completions_create.return_value = mock_chat_completion

        result = await client.query_as_user("Test prompt")

        assert result == "Test response content"
        mock_deepseek_client.chat_completions_create.assert_called_once_with(
            model="deepseek-chat",
            messages=[{"role": "user", "content": "Test prompt"}],
            temperature=0,
        )

    @pytest.mark.asyncio
    async def test_query_as_user_no_choices(self, client, mock_deepseek_client):
        """Test query_as_user with no choices in response"""
        mock_response = ChatCompletion(choices=[], usage=Usage(0, 0, 0))
        mock_deepseek_client.chat_completions_create.return_value = mock_response

        with pytest.raises(ValueError, match="DeepSeek returned no choices"):
            await client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_no_content(self, client, mock_deepseek_client):
        """Test query_as_user with no content in response"""
        message = Message(role="assistant", content=None)
        choice = Choice(message=message)
        mock_response = ChatCompletion(choices=[choice], usage=Usage(0, 0, 0))
        mock_deepseek_client.chat_completions_create.return_value = mock_response

        with pytest.raises(ValueError, match="DeepSeek returned no content"):
            await client.query_as_user("Test prompt")

    @pytest.mark.asyncio
    async def test_query_as_user_with_usage_logging(
        self, client, mock_deepseek_client, mock_chat_completion
    ):
        """Test query_as_user logs usage information with cost calculation"""
        mock_deepseek_client.chat_completions_create.return_value = mock_chat_completion

        result = await client.query_as_user("Test prompt")

        assert result == "Test response content"
        # Test that the method completes successfully with usage info

    @pytest.mark.asyncio
    async def test_deepseek_api_error_propagation(self, client, mock_deepseek_client):
        """Test that DeepSeek API errors are properly propagated"""
        mock_deepseek_client.chat_completions_create.side_effect = (
            httpx.HTTPStatusError(
                "API Error", request=MagicMock(), response=MagicMock()
            )
        )

        with pytest.raises(httpx.HTTPStatusError):
            await client.query_as_user("Test prompt")

    def test_wrapper_client_initialization(self):
        """Test wrapper client initialization"""
        mock_deepseek_client = MagicMock()
        client = Client(mock_deepseek_client)
        assert client.client == mock_deepseek_client
