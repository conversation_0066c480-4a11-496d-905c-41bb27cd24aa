import gzip
from datetime import datetime, UTC
from unittest.mock import AsyncMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.article_maker import ArticleMaker, ArticleOptions
from app.clients import s3
from app.db import models
from app.util import article

pytestmark = pytest.mark.docker


@pytest.mark.asyncio
async def test_make_from_rss(async_engine, s3_client, id_generator):
    # Given
    html = (
        "<html><body><article>"
        "<h1>Climate Change Impact on Arctic Wildlife</h1>"
        "<p>Scientists report alarming changes in polar bear habitats. "
        "<a href='/rel'>Read the full study</a> for detailed findings.</p>"
        "<p>Arctic temperatures continue to rise at twice the global rate.</p>"
        "</article></body></html>"
    )
    html_bytes = html.encode()

    feed = models.Feed(
        id=next(id_generator), url="https://feed.example.com", cr_articles=True
    )
    snapshot = models.FeedSnapshot(id=next(id_generator), feed_id=feed.id)

    page = models.Page(id=next(id_generator), url="https://page.example.org/art")
    page.update_with_content(html_bytes)

    feed_item = models.FeedItem(
        id=next(id_generator),
        feed_id=feed.id,
        snapshot_id=snapshot.id,
        web_page_id=page.id,
        guid_crc=123,
        guid="guid",
        link=page.url,
        title="Title",
    )

    news = models.News(
        id=next(id_generator),
        source_type=models.SourceTypeEnum.RSS,
        source_id=feed_item.id,
        published_at=datetime.now(UTC),
        title="Title",
        original_url=page.url,
    )

    async with AsyncSession(bind=async_engine, expire_on_commit=False) as session:
        session.add(feed)
        await session.flush()

        session.add(snapshot)
        await session.flush()

        session.add(page)
        await session.flush()

        session.add_all([feed_item, news])
        await session.commit()

    s3_client.upload_sync(s3.page_content_key(page.sha256), html_bytes)

    openai_client = AsyncMock()
    extractor = article.Extractor(openai_client)

    summarizer = AsyncMock()
    summarizer.shorten.return_value = "# Title\n\nshort"
    summarizer.summarize.return_value = "# Title\n\nsummary"

    article_id = next(id_generator)
    options = ArticleOptions(html_size_min=0, html_size_max=1000)
    maker = ArticleMaker(options, async_engine, s3_client, extractor, summarizer)
    maker.__detect_language_sync = lambda _text: "en"

    # When
    await maker.make_from_rss(article_id, feed_item.id)

    # Then
    async with AsyncSession(bind=async_engine) as session:
        article_record = await session.get(models.Article, article_id)
        assert article_record is not None
        assert article_record.title == "Title"

        news_record = await session.get(models.News, news.id)
        assert news_record.article_id == article_record.id

    def download(key: str) -> str:
        content = s3_client.get_sync(key)
        return gzip.decompress(content).decode()

    short = download(s3.article_short_key(article_id))
    original = download(s3.article_original_key(article_id))

    assert short == "# Title\n\nshort"
    assert original.startswith("# Climate Change Impact on Arctic Wildlife")
    assert "https://page.example.org/rel" in original
    assert "https://feed.example.com/rel" not in original
