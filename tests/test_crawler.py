import hashlib
import httpx
import pytest
from unittest.mock import AsyncMock
from app.crawler import Crawler
from app.db import models
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

pytestmark = pytest.mark.docker


@pytest.mark.asyncio
async def test_get_happy_path(async_engine, s3_client, id_generator):
    # Given
    url = f"https://example.com/{next(id_generator)}"
    body = b"<html>Hello, world!</html>"

    request = httpx.Request("GET", url)
    response = httpx.Response(200, content=body, request=request)

    http_client = AsyncMock()
    http_client.get = AsyncMock(return_value=response)

    crawler = Crawler(
        db_engine=async_engine,
        s3_client=s3_client,
        id_generator=id_generator,
        http_client=http_client,
    )

    # When
    page = await crawler.get(url, ttl_seconds=120)
    page2 = await crawler.get(url, ttl_seconds=120)

    # Then
    http_client.get.assert_called_once_with(url)
    assert page == page2  # Page 2 is returned from cache

    # Check page content
    assert page.url == url
    assert page.size == len(body)
    assert page.sha256 == hashlib.sha256(body).digest()

    # S3 check
    s3_key = f"mews/page_content/{page.sha256.hex()}.html"
    actual_content = s3_client.get_sync(s3_key)
    assert actual_content == body


@pytest.mark.asyncio
async def test_get_single_redirect(async_engine, s3_client, id_generator):
    # Given
    origin = f"https://example.com/{next(id_generator)}"
    target = f"https://example.org/{next(id_generator)}"
    body = b"<html>Redirect target</html>"

    # First response: 302 redirect → target
    req1 = httpx.Request("GET", origin)
    resp1 = httpx.Response(302, headers={"location": target}, request=req1)

    # Final response: 200 OK with body
    req2 = httpx.Request("GET", target)
    resp2 = httpx.Response(200, content=body, request=req2)

    async def fake_get(url):
        return {origin: resp1, target: resp2}[url]

    http_client = AsyncMock()
    http_client.get = AsyncMock(side_effect=fake_get)

    crawler = Crawler(
        db_engine=async_engine,
        s3_client=s3_client,
        id_generator=id_generator,
        http_client=http_client,
    )

    # When
    page = await crawler.get(origin, ttl_seconds=120)

    # Then
    assert http_client.get.call_count == 2
    http_client.get.assert_any_call(origin)
    http_client.get.assert_any_call(target)

    # Page should reflect the *final* URL & content
    assert page.url == target
    assert page.size == len(body)
    assert page.sha256 == hashlib.sha256(body).digest()

    s3_key = f"mews/page_content/{page.sha256.hex()}.html"
    actual_content = s3_client.get_sync(s3_key)
    assert actual_content == body

    # Origin page exists, but has different ID
    page_origin = await __find_pages_by_url(async_engine, origin)
    assert page_origin.id != page.id
    assert page_origin.url == origin
    assert page_origin.size == page.size
    assert page_origin.sha256 == page.sha256
    assert page_origin.created_at == page.created_at


async def __find_pages_by_url(async_engine, url):
    async with AsyncSession(bind=async_engine) as session:
        query = select(models.Page).where(models.Page.url == url)
        rows = await session.execute(query)
        return rows.scalar_one_or_none()
