import pytest
import pytest_asyncio
from testcontainers.postgres import <PERSON>gresContainer
from testcontainers.core.container import DockerContainer
import boto3
import botocore.exceptions
import time
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import os
import logging
import asyncpg
from sqlalchemy.engine.url import make_url
import socket
from snowflake import SnowflakeGenerator
from app import clients

logger = logging.getLogger(__name__)


async def apply_migrations(connection_url):
    logger.info("Applying migrations")

    migrations_folder = "./migrations"
    migration_scripts = [
        f"{migrations_folder}/{f}"
        for f in os.listdir(migrations_folder)
        if f.startswith("V") and f.endswith(".sql")
    ]

    # asyncpg doesn't support the simple protocol, so we need to use a sync connection
    pg_sync_url = (
        make_url(connection_url)
        .set(drivername="postgresql")
        .render_as_string(hide_password=False)
    )
    conn = await asyncpg.connect(pg_sync_url)
    try:
        for script in sorted(migration_scripts):
            logger.info(f"Running migration script: {script}")
            with open(script, "r") as f:
                sql = f.read()
                await conn.execute(sql)
    finally:
        await conn.close()

    logger.info("Migrations applied")


@pytest.fixture(scope="session")
def pg_container():
    with PostgresContainer("postgres:17", driver="asyncpg") as pg:
        yield pg


@pytest_asyncio.fixture(scope="session")
async def async_engine(pg_container):
    connection_url = pg_container.get_connection_url()

    await apply_migrations(connection_url)

    engine = create_async_engine(connection_url, echo=True, pool_size=2)

    yield engine
    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def async_session(async_engine):
    async with AsyncSession(bind=async_engine) as session:
        yield session


@pytest.fixture(scope="session")
def s3_boto3_client():
    host_port = __find_free_port()

    with DockerContainer("minio/minio:latest").with_env(
        "MINIO_ACCESS_KEY", "admin"
    ).with_env("MINIO_SECRET_KEY", "password").with_bind_ports(
        9000, host_port
    ).with_command(
        "server /data"
    ):

        endpoint_url = f"http://localhost:{host_port}"

        s3 = boto3.client(
            "s3",
            endpoint_url=endpoint_url,
            aws_access_key_id="admin",
            aws_secret_access_key="password",
        )

        __wait_for_minio(s3)

        yield s3


@pytest.fixture(scope="session")
def s3_client(s3_boto3_client):
    client = clients.s3.S3(s3_boto3_client, "mews")
    client.ensure_bucket_exists()
    return client


@pytest.fixture(scope="session")
def id_generator():
    return SnowflakeGenerator(0)


def __wait_for_minio(s3_client, timeout=10):
    """Wait until MinIO responds to list_buckets() or timeout."""
    start = time.time()
    while time.time() - start < timeout:
        try:
            s3_client.list_buckets()
            return
        except botocore.exceptions.ClientError:
            time.sleep(0.5)
        except botocore.exceptions.EndpointConnectionError:
            time.sleep(0.5)
    raise TimeoutError("MinIO did not become ready in time")


def __find_free_port():
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(("", 0))
        return s.getsockname()[1]
