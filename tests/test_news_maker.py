from app.db import models
from app.news_maker import news_from_rss, news_from_rss_list, persist_news
from datetime import datetime, UTC
import pytest
from sqlalchemy.ext.asyncio import AsyncSession


class TestNewsFromRss:
    def test_news_from_rss(self):
        # Given
        feed_item = self.__create_feed_item()

        # When
        news = news_from_rss(1, feed_item)

        # Then
        assert news.id == 1
        assert news.source_id == 1
        assert news.source_type == models.SourceTypeEnum.RSS
        assert news.published_at == news.published_at
        assert news.title == "Test"
        assert news.original_url == "http://example.com"

    def test_takes_created_at_if_published_at_is_none(self):
        # Given
        feed_item = self.__create_feed_item()
        feed_item.published_at = None

        # When
        news = news_from_rss(1, feed_item)

        # Then
        assert news.published_at == feed_item.created_at

    def takes_title_from_id_if_title_is_none(self):
        # Given
        feed_item = self.__create_feed_item()
        feed_item.title = None

        # When
        news = news_from_rss(1, feed_item)

        # Then
        assert news.title == "1"

    def __create_feed_item(self):
        created_at = datetime.now()
        published_at = datetime.now()

        return models.FeedItem(
            id=1,
            link="http://example.com",
            title="Test",
            published_at=created_at,
            created_at=published_at,
        )


def test_news_from_rss_list_filters_out_items_without_link():
    # Given
    feed_items = [
        models.FeedItem(id=1, link="http://example.com"),
        models.FeedItem(id=2, link=None),
    ]

    # When
    news = news_from_rss_list(iter([1, 2]), feed_items)

    # Then
    assert len(news) == 1
    assert news[0].id == 1


@pytest.mark.asyncio
@pytest.mark.docker
async def test_persist_news(async_engine, id_generator):
    # Given
    id1 = next(id_generator)
    id2 = next(id_generator)
    published_at_1 = datetime.now(UTC)
    published_at_2 = datetime.now(UTC)

    news = [
        models.News(
            id=id1,
            source_id=1,
            source_type=models.SourceTypeEnum.RSS,
            published_at=published_at_1,
            title="Test 1",
            original_url="http://example.com/1",
        ),
        models.News(
            id=id2,
            source_id=2,
            source_type=models.SourceTypeEnum.RSS,
            published_at=published_at_2,
            title="Test 2",
            original_url="http://example.com/2",
        ),
    ]

    # When
    await persist_news(async_engine, news)

    # Then
    async with AsyncSession(bind=async_engine) as session:
        actual1 = await session.get(models.News, id1)
        assert actual1 is not None
        assert actual1.id == id1
        assert actual1.source_id == 1
        assert actual1.source_type == models.SourceTypeEnum.RSS
        assert actual1.published_at == published_at_1
        assert actual1.title == "Test 1"
        assert actual1.original_url == "http://example.com/1"

        actual2 = await session.get(models.News, id2)
        assert actual2 is not None
        assert actual2.id == id2
        assert actual2.source_id == 2
        assert actual2.source_type == models.SourceTypeEnum.RSS
        assert actual2.published_at == published_at_2
        assert actual2.title == "Test 2"
        assert actual2.original_url == "http://example.com/2"
