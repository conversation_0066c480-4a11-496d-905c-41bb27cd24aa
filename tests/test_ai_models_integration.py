import pytest
from unittest.mock import MagicMock, patch
from app.clients import gpt, deepseek, gemini


class TestAIProviderInterfaceConsistency:
    """Test that all AI providers implement the same interface"""

    def test_ai_provider_interface_consistency(self):
        """Test that all AI providers implement the same interface"""
        # All clients should have the same interface
        openai_methods = set(dir(gpt.Client))
        deepseek_methods = set(dir(deepseek.Client))
        gemini_methods = set(dir(gemini.Client))

        # Check that all have the core query_as_user method
        assert "query_as_user" in openai_methods
        assert "query_as_user" in deepseek_methods
        assert "query_as_user" in gemini_methods

        # Check that query_as_user is an async method
        assert hasattr(gpt.Client.query_as_user, "__call__")
        assert hasattr(deepseek.Client.query_as_user, "__call__")
        assert hasattr(gemini.Client.query_as_user, "__call__")

    @pytest.mark.asyncio
    async def test_all_providers_handle_same_input(self):
        """Test that all providers can handle the same input format"""
        test_prompt = "What is the capital of France?"

        # Mock responses for each provider
        providers = [
            ("openai", gpt.Client, "OpenAI response"),
            ("deepseek", deepseek.Client, "DeepSeek response"),
            ("gemini", gemini.Client, "Gemini response"),
        ]

        for provider_name, client_class, expected_response in providers:
            with patch.object(
                client_class, "query_as_user", return_value=expected_response
            ) as mock_query:
                mock_underlying_client = MagicMock()
                client = client_class(mock_underlying_client)

                result = await client.query_as_user(test_prompt)

                assert result == expected_response
                mock_query.assert_called_once_with(test_prompt)

    @pytest.mark.asyncio
    async def test_provider_specific_model_compatibility(self):
        """Test that providers work correctly regardless of model (model is configured at client level)"""
        model_mappings = {
            "gpt-4": gpt.Client,
            "gpt-3.5-turbo": gpt.Client,
            "deepseek-chat": deepseek.Client,
            "deepseek-coder": deepseek.Client,
            "gemini-2.0-flash-exp": gemini.Client,
            "gemini-1.5-pro": gemini.Client,
        }

        for model, client_class in model_mappings.items():
            with patch.object(
                client_class, "query_as_user", return_value="test response"
            ) as mock_query:
                mock_underlying_client = MagicMock()
                client = client_class(mock_underlying_client)

                result = await client.query_as_user("Test prompt")

                assert result == "test response"
                mock_query.assert_called_once_with("Test prompt")

    def test_all_providers_have_proper_error_handling(self):
        """Test that all providers have proper error handling"""
        # Test that each client class has the required methods
        for client_class in [gpt.Client, deepseek.Client, gemini.Client]:
            # Check that the class has the required methods
            instance = client_class(MagicMock())

            # All should have the client attribute and query_as_user method
            assert hasattr(instance, "client")
            assert callable(getattr(instance, "query_as_user"))

    def test_cost_calculation_differences(self):
        """Test that different providers have different cost calculations"""
        # Check that each provider has its own pricing logic
        # (This would be in the __log_usage methods)

        # OpenAI uses the cost_usd helper function
        assert hasattr(gpt.Client, "_Client__log_usage")

        # DeepSeek has its own cost calculation
        assert hasattr(deepseek.Client, "_Client__log_usage")

        # Gemini has its own cost calculation
        assert hasattr(gemini.Client, "_Client__log_usage")

    @pytest.mark.asyncio
    async def test_concurrent_provider_usage(self):
        """Test that multiple providers can be used concurrently"""
        import asyncio

        # Create mock clients for all providers
        mock_openai = MagicMock()
        mock_deepseek = MagicMock()
        mock_gemini = MagicMock()

        openai_client = gpt.Client(mock_openai)
        deepseek_client = deepseek.Client(mock_deepseek)
        gemini_client = gemini.Client(mock_gemini)

        with patch.object(
            openai_client, "query_as_user", return_value="OpenAI response"
        ) as mock_openai_query, patch.object(
            deepseek_client, "query_as_user", return_value="DeepSeek response"
        ) as mock_deepseek_query, patch.object(
            gemini_client, "query_as_user", return_value="Gemini response"
        ) as mock_gemini_query:

            # Run all providers concurrently
            tasks = [
                openai_client.query_as_user("Test prompt"),
                deepseek_client.query_as_user("Test prompt"),
                gemini_client.query_as_user("Test prompt"),
            ]

            results = await asyncio.gather(*tasks)

            assert results[0] == "OpenAI response"
            assert results[1] == "DeepSeek response"
            assert results[2] == "Gemini response"

            # Verify all were called
            mock_openai_query.assert_called_once()
            mock_deepseek_query.assert_called_once()
            mock_gemini_query.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_handling_consistency(self):
        """Test that all providers handle errors consistently"""
        providers = [
            (gpt.Client, "OpenAI client error"),
            (deepseek.Client, "DeepSeek client error"),
            (gemini.Client, "Gemini client error"),
        ]

        for client_class, error_message in providers:
            mock_underlying_client = MagicMock()
            client = client_class(mock_underlying_client)

            # Mock the query_as_user method to raise an exception
            with patch.object(
                client, "query_as_user", side_effect=Exception(error_message)
            ):
                with pytest.raises(Exception, match=error_message):
                    await client.query_as_user("Test prompt")

    def test_client_initialization_consistency(self):
        """Test that all clients can be initialized in a consistent way"""
        mock_clients = [MagicMock(), MagicMock(), MagicMock()]

        # Test that all client classes can be initialized with a mock underlying client
        openai_client = gpt.Client(mock_clients[0])
        deepseek_client = deepseek.Client(mock_clients[1])
        gemini_client = gemini.Client(mock_clients[2])

        # Verify that the underlying clients are stored correctly
        assert openai_client.client == mock_clients[0]
        assert deepseek_client.client == mock_clients[1]
        assert gemini_client.client == mock_clients[2]

    def test_provider_specific_features(self):
        """Test that each provider has its own specific features while maintaining interface"""
        # OpenAI client should work with OpenAI models
        openai_client = gpt.Client(MagicMock())
        assert hasattr(openai_client, "client")

        # DeepSeek client should work with DeepSeek models
        deepseek_client = deepseek.Client(MagicMock())
        assert hasattr(deepseek_client, "client")

        # Gemini client should work with Gemini models
        gemini_client = gemini.Client(MagicMock())
        assert hasattr(gemini_client, "client")

    @pytest.mark.asyncio
    async def test_response_format_consistency(self):
        """Test that all providers return responses in the same format"""
        test_responses = ["Response 1", "Response 2", "Response 3"]
        providers = [gpt.Client, deepseek.Client, gemini.Client]

        for i, client_class in enumerate(providers):
            mock_underlying_client = MagicMock()
            client = client_class(mock_underlying_client)

            with patch.object(client, "query_as_user", return_value=test_responses[i]):
                result = await client.query_as_user("Test prompt")

                # All providers should return strings
                assert isinstance(result, str)
                assert result == test_responses[i]


class TestAIProviderSwitching:
    """Test AI provider switching functionality"""

    def test_can_switch_between_providers(self):
        """Test that we can switch between different AI providers"""
        # Create clients for all providers
        openai_client = gpt.Client(MagicMock())
        deepseek_client = deepseek.Client(MagicMock())
        gemini_client = gemini.Client(MagicMock())

        # Store in a dictionary to simulate provider switching
        providers = {
            "openai": openai_client,
            "deepseek": deepseek_client,
            "gemini": gemini_client,
        }

        # Test that we can access all providers
        assert "openai" in providers
        assert "deepseek" in providers
        assert "gemini" in providers

        # Test that all providers have the same interface
        for provider_name, client in providers.items():
            assert hasattr(client, "query_as_user")
            assert callable(client.query_as_user)

    @pytest.mark.asyncio
    async def test_provider_switching_maintains_state(self):
        """Test that switching providers doesn't affect individual client state"""
        # Create providers with different mock clients
        openai_mock = MagicMock()
        deepseek_mock = MagicMock()
        gemini_mock = MagicMock()

        openai_client = gpt.Client(openai_mock)
        deepseek_client = deepseek.Client(deepseek_mock)
        gemini_client = gemini.Client(gemini_mock)

        # Verify that each client maintains its own state
        assert openai_client.client is openai_mock
        assert deepseek_client.client is deepseek_mock
        assert gemini_client.client is gemini_mock

        # Verify they're independent
        assert openai_client.client is not deepseek_client.client
        assert deepseek_client.client is not gemini_client.client
        assert gemini_client.client is not openai_client.client

    def test_provider_identification(self):
        """Test that we can identify which provider is being used"""
        # Create clients
        openai_client = gpt.Client(MagicMock())
        deepseek_client = deepseek.Client(MagicMock())
        gemini_client = gemini.Client(MagicMock())

        # Test that we can identify the provider type
        assert isinstance(openai_client, gpt.Client)
        assert isinstance(deepseek_client, deepseek.Client)
        assert isinstance(gemini_client, gemini.Client)

        # Test that they're different types
        assert type(openai_client) is not type(deepseek_client)
        assert type(deepseek_client) is not type(gemini_client)
        assert type(gemini_client) is not type(openai_client)
